<?php

namespace App\Filament\Akunting\Resources;

use App\Filament\Akunting\Resources\DailyTransactionResource\Pages;
use App\Models\DailyTransaction;
use App\Models\TransactionCategory;
use App\Models\TransactionSubcategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class DailyTransactionResource extends Resource
{
    protected static ?string $model = DailyTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationLabel = 'Transaksi Harian';

    protected static ?string $navigationGroup = 'Transaksi';

    protected static ?int $navigationSort = 1;

    protected static ?string $modelLabel = 'Transaksi Harian';

    protected static ?string $pluralModelLabel = 'Transaksi Harian';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('outlet_id')
                    ->relationship('outlet', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->label('Outlet'),
                Forms\Components\DatePicker::make('transaction_date')
                    ->required()
                    ->default(now())
                    ->label('Tanggal Transaksi'),
                Forms\Components\TextInput::make('description')
                    ->required()
                    ->maxLength(255)
                    ->label('Deskripsi'),
                Forms\Components\Select::make('type')
                    ->required()
                    ->options([
                        'revenue' => 'Pendapatan',
                        'expense' => 'Pengeluaran',
                        'receivable' => 'Piutang',
                        'cash_deficit' => 'Kekurangan Kas',
                    ])
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        // Clear karyawan_id when type changes to non-applicable types
                        if (!in_array($state, ['cash_deficit', 'receivable'])) {
                            $set('karyawan_id', null);
                        }
                        // Clear payment_method for non-revenue types
                        if ($state !== 'revenue') {
                            $set('payment_method', null);
                        }
                        // Clear expense_category for non-expense types
                        if ($state !== 'expense') {
                            $set('expense_category', null);
                        }
                    })
                    ->label('Jenis Transaksi'),
                Forms\Components\Select::make('payment_method')
                    ->options(function (Get $get) {
                        $type = $get('type');

                        if ($type === 'revenue') {
                            // For revenue, get subcategories from pendapatan category
                            return \App\Models\TransactionSubcategory::active()
                                ->forCategory('pendapatan')
                                ->pluck('name', 'code')
                                ->toArray();
                        }

                        // For other types, return standard payment methods
                        return [
                            'cash' => 'Cash',
                            'transfer' => 'Transfer',
                            'debit' => 'Debit',
                            'qris' => 'QRIS',
                            'gojek' => 'Gojek',
                            'grab' => 'Grab',
                            'ovo' => 'OVO',
                        ];
                    })
                    ->visible(fn(Get $get) => in_array($get('type'), ['revenue', 'expense']))
                    ->required(fn(Get $get) => in_array($get('type'), ['revenue', 'expense']))
                    ->searchable()
                    ->label(fn(Get $get) => $get('type') === 'revenue' ? 'Jenis Pendapatan' : 'Metode Pembayaran'),
                Forms\Components\Select::make('expense_category')
                    ->options(function () {
                        return \App\Models\TransactionCategory::active()
                            ->forType('expense')
                            ->pluck('name', 'code')
                            ->toArray();
                    })
                    ->visible(fn(Get $get) => $get('type') === 'expense')
                    ->required(fn(Get $get) => $get('type') === 'expense')
                    ->live()
                    ->searchable()
                    ->afterStateUpdated(function ($state, $set) {
                        // Clear subcategory when category changes
                        $set('subcategory', null);
                    })
                    ->label('Kategori Pengeluaran')
                    ->createOptionForm([
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->label('Nama Kategori'),
                        TextInput::make('code')
                            ->required()
                            ->maxLength(50)
                            ->unique(TransactionCategory::class, 'code')
                            ->label('Kode Kategori'),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(500)
                            ->label('Deskripsi')
                            ->rows(3),
                        Forms\Components\Toggle::make('is_active')
                            ->default(true)
                            ->label('Aktif'),
                    ])
                    ->createOptionAction(
                        fn($action) => $action
                            ->label('Tambah Kategori Baru')
                            ->modalHeading('Tambah Kategori Pengeluaran')
                            ->modalSubmitActionLabel('Simpan')
                            ->modalCancelActionLabel('Batal')
                    )
                    ->createOptionUsing(function (array $data) {
                        $data['type'] = 'expense';
                        return TransactionCategory::create($data)->code;
                    }),
                Forms\Components\Select::make('subcategory')
                    ->options(function (Get $get) {
                        $categoryCode = $get('expense_category');

                        if (!$categoryCode) {
                            return [];
                        }

                        return \App\Models\TransactionSubcategory::active()
                            ->forCategory($categoryCode)
                            ->pluck('name', 'code')
                            ->toArray();
                    })
                    ->visible(fn(Get $get) => $get('type') === 'expense' && $get('expense_category'))
                    ->searchable()
                    ->label('Sub Kategori')
                    ->createOptionForm([
                        Forms\Components\Select::make('category_id')
                            ->options(function () {
                                return \App\Models\TransactionCategory::active()
                                    ->forType('expense')
                                    ->pluck('name', 'id')
                                    ->toArray();
                            })
                            ->required()
                            ->searchable()
                            ->label('Kategori Pengeluaran'),
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->label('Nama Sub Kategori'),
                        TextInput::make('code')
                            ->required()
                            ->maxLength(50)
                            ->unique(TransactionSubcategory::class, 'code')
                            ->label('Kode Sub Kategori'),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(500)
                            ->label('Deskripsi')
                            ->rows(3),
                        Forms\Components\Toggle::make('is_active')
                            ->default(true)
                            ->label('Aktif'),
                    ])
                    ->createOptionAction(
                        fn($action) => $action
                            ->label('Tambah Sub Kategori Baru')
                            ->modalHeading('Tambah Sub Kategori')
                            ->modalSubmitActionLabel('Simpan')
                            ->modalCancelActionLabel('Batal')
                    )
                    ->createOptionUsing(function (array $data, Get $get) {
                        // Auto-set category_id from parent form if not provided
                        if (empty($data['category_id'])) {
                            $expenseCategoryCode = $get('expense_category');
                            if ($expenseCategoryCode) {
                                $category = \App\Models\TransactionCategory::where('code', $expenseCategoryCode)->first();
                                if ($category) {
                                    $data['category_id'] = $category->id;
                                }
                            }
                        }
                        return TransactionSubcategory::create($data)->code;
                    }),
                Forms\Components\Select::make('karyawan_id')
                    ->relationship('karyawan', 'nama_lengkap')
                    ->searchable()
                    ->preload()
                    ->label('Karyawan (Untuk Kekurangan Kas/Piutang)')
                    ->helperText('Pilih karyawan jika transaksi terkait dengan karyawan tertentu')
                    ->visible(function (Get $get) {
                        $type = $get('type');
                        return in_array($type, ['cash_deficit', 'receivable']);
                    })
                    ->required(function (Get $get) {
                        return $get('type') === 'cash_deficit';
                    }),
                Forms\Components\TextInput::make('amount')
                    ->required()
                    ->numeric()
                    ->prefix('Rp')
                    ->minValue(0)
                    ->step(0.01)
                    ->label('Jumlah'),
                Forms\Components\Textarea::make('notes')
                    ->maxLength(65535)
                    ->columnSpanFull()
                    ->label('Catatan'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('outlet.name')
                    ->searchable()
                    ->sortable()
                    ->label('Outlet'),
                Tables\Columns\TextColumn::make('outlet.category')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'FnB' => 'success',
                        'VOO' => 'warning',
                        default => 'gray',
                    })
                    ->label('Kategori'),
                Tables\Columns\TextColumn::make('transaction_date')
                    ->date()
                    ->sortable()
                    ->label('Tanggal'),
                Tables\Columns\TextColumn::make('description')
                    ->searchable()
                    ->limit(30)
                    ->label('Deskripsi'),
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->searchable()
                    ->label('Karyawan')
                    ->placeholder('—')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->searchable()
                    ->label('Metode Bayar')
                    ->placeholder('—')
                    ->formatStateUsing(function (string $state, $record): string {
                        // Check if this is a revenue transaction with subcategory
                        if ($record->type === 'revenue') {
                            $subcategory = \App\Models\TransactionSubcategory::where('code', $state)->first();
                            if ($subcategory) {
                                return $subcategory->name;
                            }
                        }

                        // Default payment method formatting
                        return match ($state) {
                            'cash' => 'Cash',
                            'debit' => 'Debit',
                            'transfer' => 'Transfer',
                            'qris' => 'QRIS',
                            'gojek' => 'Gojek',
                            'grab' => 'Grab',
                            'ovo' => 'OVO',
                            default => $state,
                        };
                    })
                    ->toggleable(),
                Tables\Columns\TextColumn::make('expenseCategoryModel.name')
                    ->searchable()
                    ->label('Kategori')
                    ->placeholder('—')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('subcategoryModel.name')
                    ->searchable()
                    ->label('Sub Kategori')
                    ->placeholder('—')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'revenue' => 'success',
                        'expense' => 'danger',
                        'receivable' => 'warning',
                        'cash_deficit' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'revenue' => 'Pendapatan',
                        'expense' => 'Pengeluaran',
                        'receivable' => 'Piutang',
                        'cash_deficit' => 'Kekurangan Kas',
                        default => $state,
                    })
                    ->label('Jenis'),
                Tables\Columns\TextColumn::make('amount')
                    ->money('IDR')
                    ->sortable()
                    ->label('Jumlah'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Dibuat'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('outlet_id')
                    ->relationship('outlet', 'name')
                    ->searchable()
                    ->preload()
                    ->label('Outlet'),
                Tables\Filters\SelectFilter::make('outlet.category')
                    ->options([
                        'FnB' => 'Food & Beverage (FnB)',
                        'VOO' => 'Viera Oleh-oleh (VOO)',
                    ])
                    ->query(function ($query, array $data) {
                        if (isset($data['value']) && $data['value'] !== '') {
                            return $query->whereHas('outlet', function ($q) use ($data) {
                                $q->where('category', $data['value']);
                            });
                        }
                        return $query;
                    })
                    ->label('Kategori'),
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'revenue' => 'Pendapatan',
                        'expense' => 'Pengeluaran',
                        'receivable' => 'Piutang',
                        'cash_deficit' => 'Kekurangan Kas',
                    ])
                    ->label('Jenis Transaksi'),
                Tables\Filters\Filter::make('transaction_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when($data['from'], fn($query, $date) => $query->whereDate('transaction_date', '>=', $date))
                            ->when($data['until'], fn($query, $date) => $query->whereDate('transaction_date', '<=', $date));
                    }),
                SelectFilter::make('month')
                    ->options([
                        1 => 'Januari',
                        2 => 'Februari',
                        3 => 'Maret',
                        4 => 'April',
                        5 => 'Mei',
                        6 => 'Juni',
                        7 => 'Juli',
                        8 => 'Agustus',
                        9 => 'September',
                        10 => 'Oktober',
                        11 => 'November',
                        12 => 'Desember'
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value']) && $data['value'] !== '') {
                            return $query->whereMonth('transaction_date', $data['value']);
                        }
                        return $query;
                    })
                    ->default(now()->month)
                    ->label('Bulan'),
                SelectFilter::make('year')
                    ->options(function () {
                        $years = [];
                        for ($year = date('Y') - 5; $year <= date('Y') + 1; $year++) {
                            $years[$year] = $year;
                        }
                        return $years;
                    })
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value']) && $data['value'] !== '') {
                            return $query->whereYear('transaction_date', $data['value']);
                        }
                        return $query;
                    })
                    ->default(now()->year)
                    ->label('Tahun'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('transaction_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getWidgets(): array
    {
        return [
            DailyTransactionResource\Widgets\TransactionStatsWidget::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDailyTransactions::route('/'),
            'create' => Pages\CreateDailyTransaction::route('/create'),
            'edit' => Pages\EditDailyTransaction::route('/{record}/edit'),
        ];
    }
}
