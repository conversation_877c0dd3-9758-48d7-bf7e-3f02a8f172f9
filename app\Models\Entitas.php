<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Entitas extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'entitas';

    protected $fillable = [
        'nama',
        'alamat',
        'keterangan',
        'latitude',
        'longitude',
        'radius',
        'enable_geofencing',
    ];

    protected $dates = ['deleted_at'];

    protected $casts = [
        'latitude' => 'decimal:17',
        'longitude' => 'decimal:17',
        'radius' => 'integer',
        'enable_geofencing' => 'boolean',
    ];

    /**
     * Calculate distance between two coordinates using Haversine formula
     *
     * @param float $lat1 Latitude 1
     * @param float $lon1 Longitude 1
     * @param float $lat2 Latitude 2
     * @param float $lon2 Longitude 2
     * @return float Distance in meters
     */
    public static function calculateDistance($lat1, $lon1, $lat2, $lon2): float
    {
        $earthRadius = 6371000; // Earth radius in meters

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Check if given coordinates are within allowed radius
     *
     * @param float $userLat User's latitude
     * @param float $userLon User's longitude
     * @return array ['allowed' => bool, 'distance' => float, 'radius' => int]
     */
    public function isWithinRadius($userLat, $userLon): array
    {
        if (!$this->enable_geofencing || !$this->latitude || !$this->longitude) {
            return [
                'allowed' => true,
                'distance' => 0,
                'radius' => $this->radius ?? 100,
                'message' => 'Geofencing disabled or coordinates not set'
            ];
        }

        $distance = self::calculateDistance(
            $this->latitude,
            $this->longitude,
            $userLat,
            $userLon
        );

        $allowed = $distance <= $this->radius;

        return [
            'allowed' => $allowed,
            'distance' => round($distance, 2),
            'radius' => $this->radius,
            'message' => $allowed
                ? 'Lokasi dalam radius yang diperbolehkan'
                : "Anda berada {$distance}m dari lokasi kerja (max: {$this->radius}m)"
        ];
    }

    /**
     * Get formatted coordinates
     */
    public function getCoordinatesAttribute(): ?string
    {
        if ($this->latitude && $this->longitude) {
            return "{$this->latitude}, {$this->longitude}";
        }
        return null;
    }

    /**
     * Relationship with karyawan
     */
    public function karyawan()
    {
        return $this->hasMany(Karyawan::class, 'id_entitas');
    }

    /**
     * Relationship with departemen
     */
    public function departemen()
    {
        return $this->hasMany(Departemen::class, 'entitas_id');
    }

    /**
     * Relationship with inventory stocks
     */
    public function inventoryStocks()
    {
        return $this->hasMany(InventoryStock::class);
    }

    /**
     * Relationship with sales transactions
     */
    public function salesTransactions()
    {
        return $this->hasMany(SalesTransaction::class);
    }
}
