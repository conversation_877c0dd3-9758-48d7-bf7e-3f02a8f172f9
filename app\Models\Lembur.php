<?php

namespace App\Models;

use EightyNine\Approvals\Models\ApprovableModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Lembur extends ApprovableModel
{
    use HasFactory, SoftDeletes, \App\Traits\HasOptimizedQueries;

    protected $table = 'lembur';

    protected $fillable = [
        'karyawan_id',
        'tanggal',
        'jumlah_jam',
        'deskripsi',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'tanggal'];

    // Relationship dengan Karyawan
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class, 'karyawan_id');
    }

    // Relationship dengan User (creator)
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scope untuk filter berdasarkan bulan
    public function scopeFilterByMonth($query, $month, $year = null)
    {
        $year = $year ?? date('Y');
        return $query->whereMonth('tanggal', $month)
                    ->whereYear('tanggal', $year);
    }

    // Scope untuk filter berdasarkan karyawan
    public function scopeFilterByKaryawan($query, $karyawanId)
    {
        return $query->where('karyawan_id', $karyawanId);
    }
}
