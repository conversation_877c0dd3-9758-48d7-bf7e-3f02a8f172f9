<?php

namespace App\Filament\Pages;

use App\Services\PermissionService;
use Illuminate\Support\Facades\Auth;

class SupervisorDashboard extends Dashboard
{

    protected static ?string $title = 'Dashboard Supervisor';

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationLabel = 'Dashboard Supervisor';
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    protected static ?int $navigationSort = 1;
    protected static string $routePath = '/supervisor-dashboard';
    protected static string $view = 'filament.pages.supervisor-dashboard';

    public function getWidgets(): array
    {
        return [
            \App\Filament\Widgets\SupervisorStatsOverview::class,
            \App\Filament\Widgets\ScheduleTable::class,
        ];
    }

    public function getColumns(): int | string | array
    {
        return [
            'sm' => 1,
            'md' => 2,
            'lg' => 3,
            'xl' => 4,
        ];
    }



    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }
}
