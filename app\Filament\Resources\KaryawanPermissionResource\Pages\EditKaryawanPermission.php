<?php

namespace App\Filament\Resources\KaryawanPermissionResource\Pages;

use App\Filament\Resources\KaryawanPermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditKaryawanPermission extends EditRecord
{
    protected static string $resource = KaryawanPermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Populate scope values to appropriate fields for editing
        if (isset($data['scope_values']) && is_array($data['scope_values'])) {
            switch ($data['scope_type']) {
                case 'entitas':
                    $data['scope_values_entitas'] = $data['scope_values'];
                    break;
                case 'departemen':
                    $data['scope_values_departemen'] = $data['scope_values'];

                    // Populate individual entitas departemen fields
                    $entitas = \App\Models\Entitas::all();
                    foreach ($entitas as $ent) {
                        // Get departemen yang ada di entitas ini melalui karyawan
                        $entDepartemenIds = \App\Models\Karyawan::where('id_entitas', $ent->id)
                            ->whereNotNull('id_departemen')
                            ->distinct()
                            ->pluck('id_departemen')
                            ->toArray();

                        $selectedInEnt = array_intersect($data['scope_values'], $entDepartemenIds);
                        $data["departemen_entitas_{$ent->id}"] = $selectedInEnt;
                    }
                    break;
                case 'divisi':
                    $data['scope_values_divisi'] = $data['scope_values'];

                    // Populate individual department divisi fields
                    $departemen = \App\Models\Departemen::with('divisi')->get();
                    foreach ($departemen as $dept) {
                        $deptDivisiIds = $dept->divisi->pluck('id')->toArray();
                        $selectedInDept = array_intersect($data['scope_values'], $deptDivisiIds);
                        $data["divisi_dept_{$dept->id}"] = $selectedInDept;
                    }
                    break;
                case 'custom':
                    $data['scope_values_custom'] = $data['scope_values'];
                    break;
            }
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Handle scope values based on scope type
        switch ($data['scope_type']) {
            case 'entitas':
                $data['scope_values'] = $data['scope_values_entitas'] ?? null;
                break;

            case 'departemen':
                // Combine departemen from all entitas
                $allDepartemen = [];
                $entitas = \App\Models\Entitas::all();
                foreach ($entitas as $ent) {
                    $entDepartemen = $data["departemen_entitas_{$ent->id}"] ?? [];
                    if (is_array($entDepartemen)) {
                        $allDepartemen = array_merge($allDepartemen, $entDepartemen);
                    }
                }
                $data['scope_values'] = !empty($allDepartemen) ? array_unique($allDepartemen) : null;
                break;

            case 'divisi':
                // Combine divisi from all departemen
                $allDivisi = [];
                $departemen = \App\Models\Departemen::all();
                foreach ($departemen as $dept) {
                    $deptDivisi = $data["divisi_dept_{$dept->id}"] ?? [];
                    if (is_array($deptDivisi)) {
                        $allDivisi = array_merge($allDivisi, $deptDivisi);
                    }
                }
                $data['scope_values'] = !empty($allDivisi) ? array_unique($allDivisi) : null;
                break;

            case 'custom':
                $data['scope_values'] = $data['scope_values_custom'] ?? null;
                break;

            case 'all':
            default:
                $data['scope_values'] = null;
                break;
        }

        // Remove temporary fields
        unset($data['scope_values_entitas']);
        unset($data['scope_values_departemen']);
        unset($data['scope_values_divisi']);
        unset($data['scope_values_custom']);

        // Remove individual entitas departemen fields
        $entitas = \App\Models\Entitas::all();
        foreach ($entitas as $ent) {
            unset($data["departemen_entitas_{$ent->id}"]);
        }

        // Remove individual department divisi fields
        $departemen = \App\Models\Departemen::all();
        foreach ($departemen as $dept) {
            unset($data["divisi_dept_{$dept->id}"]);
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
