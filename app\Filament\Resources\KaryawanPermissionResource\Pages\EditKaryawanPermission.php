<?php

namespace App\Filament\Resources\KaryawanPermissionResource\Pages;

use App\Filament\Resources\KaryawanPermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditKaryawanPermission extends EditRecord
{
    protected static string $resource = KaryawanPermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Populate scope values to appropriate fields for editing
        if (isset($data['scope_values']) && is_array($data['scope_values'])) {
            switch ($data['scope_type']) {
                case 'entitas':
                    $data['scope_values_entitas'] = $data['scope_values'];
                    break;
                case 'departemen':
                    $data['scope_values_departemen'] = $data['scope_values'];
                    break;
                case 'divisi':
                    $data['scope_values_divisi'] = $data['scope_values'];
                    break;
                case 'custom':
                    $data['scope_values_custom'] = $data['scope_values'];
                    break;
            }
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Handle scope values based on scope type
        switch ($data['scope_type']) {
            case 'entitas':
                $data['scope_values'] = $data['scope_values_entitas'] ?? null;
                break;
            case 'departemen':
                $data['scope_values'] = $data['scope_values_departemen'] ?? null;
                break;
            case 'divisi':
                $data['scope_values'] = $data['scope_values_divisi'] ?? null;
                break;
            case 'custom':
                $data['scope_values'] = $data['scope_values_custom'] ?? null;
                break;
            case 'all':
            default:
                $data['scope_values'] = null;
                break;
        }

        // Remove temporary fields
        unset($data['scope_values_entitas']);
        unset($data['scope_values_departemen']);
        unset($data['scope_values_divisi']);
        unset($data['scope_values_custom']);

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
