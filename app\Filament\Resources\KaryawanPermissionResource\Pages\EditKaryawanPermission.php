<?php

namespace App\Filament\Resources\KaryawanPermissionResource\Pages;

use App\Filament\Resources\KaryawanPermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditKaryawanPermission extends EditRecord
{
    protected static string $resource = KaryawanPermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Populate scope values to appropriate fields for editing
        if (isset($data['scope_values']) && is_array($data['scope_values'])) {
            switch ($data['scope_type']) {
                case 'entitas':
                    $data['scope_values_entitas'] = $data['scope_values'];
                    break;
                case 'departemen':
                    // Handle new structure: array of objects with departemen_id and entitas_id
                    if (is_array($data['scope_values'])) {
                        $entitas = \App\Models\Entitas::all();
                        foreach ($entitas as $ent) {
                            $selectedDepartemen = [];
                            foreach ($data['scope_values'] as $scope) {
                                if (is_array($scope) && isset($scope['departemen_id'], $scope['entitas_id'])) {
                                    if ($scope['entitas_id'] == $ent->id) {
                                        $selectedDepartemen[] = $scope['departemen_id'];
                                    }
                                }
                            }
                            $data["departemen_entitas_{$ent->id}"] = $selectedDepartemen;
                        }
                    }
                    break;
                case 'divisi':
                    // Handle both old and new structure for divisi
                    if (is_array($data['scope_values'])) {
                        $entitas = \App\Models\Entitas::all();
                        foreach ($entitas as $ent) {
                            // Get departemen yang ada di entitas ini
                            $departemenIds = \App\Models\Karyawan::where('id_entitas', $ent->id)
                                ->whereNotNull('id_departemen')
                                ->distinct()
                                ->pluck('id_departemen')
                                ->toArray();

                            $departemen = \App\Models\Departemen::whereIn('id', $departemenIds)->get();
                            foreach ($departemen as $dept) {
                                $selectedDivisi = [];
                                foreach ($data['scope_values'] as $scope) {
                                    if (is_array($scope) && isset($scope['divisi_id'], $scope['departemen_id'])) {
                                        // Old format with context
                                        if ($scope['departemen_id'] == $dept->id) {
                                            $selectedDivisi[] = $scope['divisi_id'];
                                        }
                                    } elseif (is_numeric($scope)) {
                                        // New format (simple divisi ID) - check if divisi belongs to this dept
                                        $divisi = \App\Models\Divisi::where('id', $scope)
                                            ->where('id_departemen', $dept->id)
                                            ->first();
                                        if ($divisi) {
                                            $selectedDivisi[] = $scope;
                                        }
                                    }
                                }
                                $data["divisi_dept_{$dept->id}_entitas_{$ent->id}"] = $selectedDivisi;
                            }
                        }
                    }
                    break;
                case 'custom':
                    $data['scope_values_custom'] = $data['scope_values'];
                    break;
            }
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Handle scope values based on scope type
        switch ($data['scope_type']) {
            case 'entitas':
                $data['scope_values'] = $data['scope_values_entitas'] ?? null;
                break;

            case 'departemen':
                // Combine departemen from all entitas with entitas context
                $departemenWithEntitas = [];
                $entitas = \App\Models\Entitas::all();
                foreach ($entitas as $ent) {
                    $entDepartemen = $data["departemen_entitas_{$ent->id}"] ?? [];
                    if (is_array($entDepartemen) && !empty($entDepartemen)) {
                        foreach ($entDepartemen as $deptId) {
                            $departemenWithEntitas[] = [
                                'departemen_id' => $deptId,
                                'entitas_id' => $ent->id
                            ];
                        }
                    }
                }
                $data['scope_values'] = !empty($departemenWithEntitas) ? $departemenWithEntitas : null;
                break;

            case 'divisi':
                // Combine divisi from all departemen with entitas context
                $divisiWithContext = [];
                $entitas = \App\Models\Entitas::all();
                foreach ($entitas as $ent) {
                    // Get departemen yang ada di entitas ini
                    $departemenIds = \App\Models\Karyawan::where('id_entitas', $ent->id)
                        ->whereNotNull('id_departemen')
                        ->distinct()
                        ->pluck('id_departemen')
                        ->toArray();

                    $departemen = \App\Models\Departemen::whereIn('id', $departemenIds)->get();
                    foreach ($departemen as $dept) {
                        $deptDivisi = $data["divisi_dept_{$dept->id}_entitas_{$ent->id}"] ?? [];
                        if (is_array($deptDivisi) && !empty($deptDivisi)) {
                            foreach ($deptDivisi as $divisiId) {
                                $divisiWithContext[] = [
                                    'divisi_id' => $divisiId,
                                    'departemen_id' => $dept->id,
                                    'entitas_id' => $ent->id
                                ];
                            }
                        }
                    }
                }
                $data['scope_values'] = !empty($divisiWithContext) ? $divisiWithContext : null;
                break;

            case 'custom':
                $data['scope_values'] = $data['scope_values_custom'] ?? null;
                break;

            case 'all':
            default:
                $data['scope_values'] = null;
                break;
        }

        // Remove temporary fields
        unset($data['scope_values_entitas']);
        unset($data['scope_values_departemen']);
        unset($data['scope_values_divisi']);
        unset($data['scope_values_custom']);

        // Remove individual entitas departemen fields
        $entitas = \App\Models\Entitas::all();
        foreach ($entitas as $ent) {
            unset($data["departemen_entitas_{$ent->id}"]);

            // Remove divisi fields for this entitas
            $departemenIds = \App\Models\Karyawan::where('id_entitas', $ent->id)
                ->whereNotNull('id_departemen')
                ->distinct()
                ->pluck('id_departemen')
                ->toArray();

            $departemen = \App\Models\Departemen::whereIn('id', $departemenIds)->get();
            foreach ($departemen as $dept) {
                unset($data["divisi_dept_{$dept->id}_entitas_{$ent->id}"]);
            }
        }

        // Remove individual department divisi fields (legacy cleanup)
        $departemen = \App\Models\Departemen::all();
        foreach ($departemen as $dept) {
            unset($data["divisi_dept_{$dept->id}"]);
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
