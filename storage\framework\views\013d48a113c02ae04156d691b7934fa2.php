<div>
    <!--[if BLOCK]><![endif]--><?php if($getRecord()->approvalStatus): ?>
        <p class="px-3">
            <small>
                <?php if($getRecord()->isApprovalCompleted()): ?>
                    <div class="flex gap-x-4 ml-auto">
                        <span
                            class="px-3 py-1  rounded-full text-xs
                    <?php if($getRecord()->lastApproval->approval_action == 'Approved'): ?> bg-green-500 text-white-800 <?php endif; ?>
                    <?php if($getRecord()->lastApproval->approval_action == 'Discarded'): ?> bg-red-500 text-white <?php endif; ?>
                    <?php if($getRecord()->lastApproval->approval_action == 'Rejected'): ?> bg-red-500 text-white <?php endif; ?>
                    <?php if($getRecord()->lastApproval->approval_action == 'Discarded'): ?> bg-danger-500 text-white <?php endif; ?>
                    <?php if($getRecord()->lastApproval->approval_action == 'Pending'): ?> bg-warning-500 text-yellow-800 <?php endif; ?>
                    <?php if($getRecord()->lastApproval->approval_action == 'Submitted'): ?> bg-blue-500 text-white <?php endif; ?>">
                            <?php echo e(__('filament-approvals::approvals.actions.history.' . $getRecord()->lastApproval->approval_action)); ?>

                        </span>
                    </div>
                    
                <?php else: ?>
                <?php if($getRecord()->isSubmitted()): ?>
                <div class="flex gap-x-4 ml-auto">
                        <span
                            class="px-3 py-1  rounded-full text-xs
                    <?php if($getRecord()->lastApproval->approval_action == 'Approved'): ?> bg-green-500 text-white-800 <?php endif; ?>
                    <?php if($getRecord()->lastApproval->approval_action == 'Discarded'): ?> bg-red-500 text-white <?php endif; ?>
                    <?php if($getRecord()->lastApproval->approval_action == 'Rejected'): ?> bg-red-500 text-white <?php endif; ?>
                    <?php if($getRecord()->lastApproval->approval_action == 'Discarded'): ?> bg-danger-500 text-white <?php endif; ?>
                    <?php if($getRecord()->lastApproval->approval_action == 'Pending'): ?> bg-warning-500 text-yellow-800 <?php endif; ?>
                    <?php if($getRecord()->lastApproval->approval_action == 'Submitted'): ?> bg-blue-500 text-white <?php endif; ?>">
                            <?php echo e(__('filament-approvals::approvals.actions.history.' . $getRecord()->lastApproval->approval_action)); ?>

                        </span>
                    </div>
                <?php else: ?>
                <?php echo e(('Belum Dikirim')); ?>

                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </small>
        </p>
        
    <?php else: ?>
        <span class="px-3 py-1 bg-gray-200 text-gray-800 rounded-full text-xs">
            <?php echo e(__('filament-approvals::approvals.status_column.approval_status_does_not_exist')); ?>

        </span>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH D:\laragon\www\viera\resources\views/vendor/filament-approvals/tables/columns/approval-status-column.blade.php ENDPATH**/ ?>