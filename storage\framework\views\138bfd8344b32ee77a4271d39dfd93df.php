<div>
    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $a): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="p-4 bg-gray-50 mb-4 rounded border-1">
            <div class="flex items-center gap-x-3">
                <img src="<?php echo e($a->user ? Filament\Facades\Filament::getUserAvatarUrl($a->user) : ''); ?>"
                    class="h-6 w-6 flex-none rounded-full bg-gray-800" alt="Avatar" />
                <h3 class="flex truncate text-sm font-semibold leading-6 text-gray-500">
                    <?php echo e($a->user?->name ?? 'Unknown User'); ?>

                </h3>
                <time class="flex-none text-xs text-gray-500">
                    <!--[if BLOCK]><![endif]--><?php if($a->created_at): ?>
                        <?php echo e($a->created_at->diffForHumans()); ?> - <?php echo e($a->created_at); ?>

                    <?php else: ?>
                        Unknown Date
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </time>
                <div class="flex gap-x-4 ml-auto">
                    <span
                        class="px-3 py-1  rounded-full text-xs
<?php
$approvalAction = $a->approval_action ?? '';
    $statusClasses = [
        'Approved' => 'bg-green-500 text-white',
        'Rejected' => 'bg-red-500 text-white',
        'Discarded' => 'bg-red-500 text-white',
        'Pending' => 'bg-warning-500 text-yellow-800',
        'Submitted' => 'bg-blue-500 text-white',
    ];
    $classes = $statusClasses[$approvalAction] ?? 'bg-gray-500 text-white'; ?>

class="<?php echo e($classes); ?>">
                        <?php echo e(__('filament-approvals::approvals.actions.history.' . ($a->approval_action ?? 'unknown'))); ?>

                    </span>
                </div>
            </div>
            <!--[if BLOCK]><![endif]--><?php if($a->comment): ?>
                <div class="mt-3 text-sm"><?php echo $a->comment; ?></div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH D:\laragon\www\viera\resources\views/vendor/filament-approvals/tables/columns/approval-status-column-action-view.blade.php ENDPATH**/ ?>