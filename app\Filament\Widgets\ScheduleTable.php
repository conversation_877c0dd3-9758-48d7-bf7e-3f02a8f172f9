<?php

namespace App\Filament\Widgets;

use App\Models\Schedule;
use App\Models\Karyawan;
use App\Services\PermissionService;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ScheduleTable extends BaseWidget
{
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->heading('Jadwal Hari Ini')
            ->query(function (): Builder {
                $user = Auth::user();
                $today = Carbon::today()->format('Y-m-d');

                $query = Schedule::query()
                    ->where('tanggal_jadwal', $today)
                    ->with(['karyawan', 'shift', 'absensi'])
                    ->orderBy('waktu_masuk');

                // Apply permission-based filtering
                if ($user->hasRole(['super_admin', 'direktur'])) {
                    // Super admin and direktur can see all schedules
                    return $query;
                } elseif ($user->hasRole('manager_hrd')) {
                    // Manager HRD can see all schedules
                    return $query;
                } else {
                    // Use permission service to filter accessible schedules
                    return PermissionService::applyPermissionFilter($query, 'manage_jadwal', 'karyawan_id');
                }
            })
            ->columns([
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->label('Nama Karyawan')
                    ->searchable()
                    ->sortable(),

                // dept, div, jabatan
                Tables\Columns\TextColumn::make('karyawan.departemen.nama_departemen')
                    ->label('Departemen')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('karyawan.divisi.nama_divisi')
                    ->label('Divisi')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('karyawan.jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('karyawan.entitas.nama')
                    ->label('Entitas')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('shift.nama_shift')
                    ->label('Shift')
                    ->sortable(),

                Tables\Columns\TextColumn::make('waktu_masuk')
                    ->label('Masuk')
                    ->time('H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('waktu_keluar')
                    ->label('Keluar')
                    ->time('H:i')
                    ->sortable(),

                // absensi waktu_masuk
                Tables\Columns\TextColumn::make('absensi.waktu_masuk')
                    ->label('Absen Masuk')
                    ->time('H:i')
                    ->sortable(),

                // absensi waktu_keluar
                Tables\Columns\TextColumn::make('absensi.waktu_keluar')
                    ->label('Absen Keluar')
                    ->time('H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Hadir' => 'success',
                        'Libur' => 'info',
                        'Cuti' => 'warning',
                        'Izin' => 'warning',
                        'Sakit' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                // Tables\Columns\IconColumn::make('absensi.approved_at')
                //     ->label('Disetujui')
                //     ->boolean()
                //     ->sortable(),

                Tables\Columns\TextColumn::make('absensi.status')
                    ->label('Status Absensi')
                    ->badge()
                    ->color(fn($state): string => match ($state) {
                        'hadir' => 'success',
                        'terlambat' => 'warning',
                        'izin' => 'info',
                        'sakit' => 'info',
                        'cuti' => 'primary',
                        'alpha' => 'danger',
                        null => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn($state): string => $state ? ucfirst($state) : 'Belum Absen')
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('Lihat')
                    ->url(fn(Schedule $record): string => $record->absensi ? route('filament.admin.resources.absensis.view', ['record' => $record->absensi->id]) : '#')
                    ->visible(fn(Schedule $record): bool => $record->absensi !== null),
                Tables\Actions\Action::make('record_attendance')
                    ->label('Catat Absensi')
                    ->url(fn(Schedule $record): string => route('filament.admin.resources.absensis.create', ['karyawan_id' => $record->karyawan_id, 'tanggal_absensi' => $record->tanggal_jadwal]))
                    ->visible(fn(Schedule $record): bool => !$record->absensi),
            ])
            ->emptyStateHeading('Tidak ada jadwal hari ini')
            ->emptyStateDescription('Belum ada jadwal yang dibuat untuk hari ini.')
            ->emptyStateIcon('heroicon-o-calendar')
            ->paginated(false);
    }
}
