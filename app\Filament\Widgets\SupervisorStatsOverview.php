<?php

namespace App\Filament\Widgets;

use App\Models\Absensi;
use App\Models\Karyawan;
use App\Models\Schedule;
use App\Models\User;
use App\Services\PermissionService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class SupervisorStatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;

    // Only show this widget based on permissions
    public static function canView(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Allow super admin and direktur
        if ($user->hasRole(['super_admin', 'direktur'])) {
            return true;
        }

        // Allow manager_hrd role
        if ($user->hasRole('manager_hrd')) {
            return true;
        }

        // Check if user has permission to view reports or absensi
        return PermissionService::hasPermission('view_reports') ||
            PermissionService::hasPermission('view_absensi');
    }

    protected function getStats(): array
    {
        $user = Auth::user();
        $thisMonth = now()->format('Y-m');

        // Get accessible employee IDs based on permissions
        $employeeIds = [];

        // Super admin and direktur can see all employees
        if ($user->hasRole(['super_admin', 'direktur'])) {
            $employeeIds = Karyawan::where('status_aktif', 1)->pluck('id')->toArray();
        }
        // Manager HRD can see all employees
        elseif ($user->hasRole('manager_hrd')) {
            $employeeIds = Karyawan::where('status_aktif', 1)->pluck('id')->toArray();
        }
        // Use permission service to get accessible employee IDs
        else {
            $employeeIds = PermissionService::getAccessibleKaryawanIds('view_absensi');
        }

        // Count total accessible employees
        $totalEmployees = count($employeeIds);

        // Count this month's attendance
        $monthlyAttendanceCount = Absensi::whereIn('karyawan_id', $employeeIds)
            ->whereRaw("DATE_FORMAT(tanggal_absensi, '%Y-%m') = ?", [$thisMonth])
            ->count();

        // Count this month's late attendance
        $monthlyLateCount = Absensi::whereIn('karyawan_id', $employeeIds)
            ->whereRaw("DATE_FORMAT(tanggal_absensi, '%Y-%m') = ?", [$thisMonth])
            ->where('status', 'terlambat')
            ->count();

        // Calculate late percentage
        $latePercentage = $monthlyAttendanceCount > 0
            ? round(($monthlyLateCount / $monthlyAttendanceCount) * 100, 2)
            : 0;

        // Count pending approval attendance
        $pendingApprovalCount = Absensi::whereIn('karyawan_id', $employeeIds)
            ->whereNull('approved_at')
            ->count();

        return [
            Stat::make('Total Karyawan', $totalEmployees)
                ->description('Jumlah karyawan yang diawasi')
                ->color('primary'),

            Stat::make('Absensi Bulan Ini', $monthlyAttendanceCount)
                ->description('Total absensi bulan ' . now()->translatedFormat('F Y'))
                ->color('success'),

            Stat::make('Persentase Keterlambatan', $latePercentage . '%')
                ->description('Persentase keterlambatan bulan ini')
                ->color($latePercentage > 10 ? 'danger' : ($latePercentage > 5 ? 'warning' : 'success')),

            Stat::make('Menunggu Persetujuan', $pendingApprovalCount)
                ->description('Absensi yang belum disetujui')
                ->color($pendingApprovalCount > 0 ? 'warning' : 'success'),
        ];
    }
}
