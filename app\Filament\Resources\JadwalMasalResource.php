<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JadwalMasalResource\Pages;
use App\Models\JadwalMasal;
use App\Models\Karyawan;
use App\Models\Shift;
use App\Models\Entitas;
use App\Models\Departemen;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Hidden;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Tables\Columns\TextColumn;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Section;
use App\Services\PermissionService;
use Carbon\Carbon;

class JadwalMasalResource extends Resource
{
    protected static ?string $model = JadwalMasal::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar';
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    protected static ?string $navigationLabel = 'Jadwal Masal';
    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Jadwal')
                    ->schema([
                        TextInput::make('nama_jadwal')
                            ->label('Nama Jadwal')
                            ->required()
                            ->maxLength(255),

                        DatePicker::make('tanggal_mulai')
                            ->label('Tanggal Mulai')
                            ->required(),

                        DatePicker::make('tanggal_selesai')
                            ->label('Tanggal Selesai')
                            ->required()
                            ->minDate(fn(Forms\Get $get) => $get('tanggal_mulai')),

                        Select::make('shift_id')
                            ->label('Shift')
                            ->helperText('Waktu masuk dan keluar akan diambil dari shift yang dipilih, kalau dua shift juga akan diambil')
                            ->options(function () {
                                return Shift::where('is_active', true)
                                    ->get()
                                    ->mapWithKeys(function ($shift) {
                                        return [
                                            $shift->id => $shift->nama_shift . ' (' .
                                                $shift->waktu_mulai->format('H:i') . ' - ' .
                                                $shift->waktu_selesai->format('H:i') .
                                                ($shift->is_split_shift ? ' | P2: ' . $shift->waktu_mulai_periode2->format('H:i') . ' - ' . $shift->waktu_selesai_periode2->format('H:i') : '') .
                                                ')'
                                        ];
                                    });
                            })
                            ->required()
                            ->searchable(),

                        Textarea::make('keterangan')
                            ->label('Keterangan')
                            ->maxLength(1000),

                        Select::make('entitas_id')
                            ->label('Entitas')
                            ->placeholder('Pilih entitas untuk jadwal masal ini')
                            ->options(Entitas::orderBy('nama')->pluck('nama', 'id'))
                            ->searchable()
                            ->required()
                            ->reactive()
                            ->default(function () {
                                $user = Auth::user();

                                // Auto-set entitas_id berdasarkan role user sebagai default
                                if ($user->role === 'keptok' && $user->karyawan && $user->karyawan->id_entitas) {
                                    return $user->karyawan->id_entitas;
                                } elseif ($user->role === 'supervisor' && $user->karyawan && $user->karyawan->id_divisi) {
                                    // Untuk supervisor, ambil entitas pertama dari divisi mereka
                                    $entitasId = \App\Models\Karyawan::where('id_divisi', $user->karyawan->id_divisi)
                                        ->whereNotNull('id_entitas')
                                        ->value('id_entitas');
                                    return $entitasId;
                                } elseif ($user->role === 'manager' && $user->karyawan && $user->karyawan->id_departemen) {
                                    // Untuk manager, ambil entitas pertama dari departemen mereka
                                    $entitasId = \App\Models\Karyawan::where('id_departemen', $user->karyawan->id_departemen)
                                        ->whereNotNull('id_entitas')
                                        ->value('id_entitas');
                                    return $entitasId;
                                }

                                return null;
                            })
                            ->afterStateUpdated(function (callable $set) {
                                // Reset karyawan selection when entitas changes
                                $set('karyawan', json_encode([]));
                            })
                            ->helperText('Pilih entitas tempat jadwal masal ini akan diterapkan'),

                        TextInput::make('generation_status')
                            ->label('Status Generate')
                            ->disabled()
                            ->dehydrated(false)
                            ->default(fn($record) => $record ? $record->getGenerationStatusText() : 'Belum di-generate')
                            ->visible(fn($record) => $record !== null)
                            ->helperText('Status apakah jadwal masal ini sudah pernah di-generate menjadi jadwal individual'),
                    ])->columns(2),

                Section::make('Pilih Karyawan')
                    ->schema([



                        \Filament\Forms\Components\View::make('filament.forms.karyawan-divisi-selector')
                            ->reactive()
                            ->viewData(function (callable $get) {
                                $entitasId = $get('entitas_id');

                                // Get accessible karyawan IDs using PermissionService
                                $accessibleIds = PermissionService::getAccessibleKaryawanIds('manage_jadwal');

                                if (empty($accessibleIds)) {
                                    return ['divisiGroups' => collect(), 'selectedKaryawan' => []];
                                }

                                // Get karyawan with complete hierarchy information
                                $query = Karyawan::with(['entitas', 'departemen', 'divisi'])
                                    ->whereIn('id', $accessibleIds)
                                    ->where('status_aktif', 1);

                                // Apply entitas filter if selected
                                if ($entitasId) {
                                    $query->where('id_entitas', $entitasId);
                                }

                                $karyawanList = $query->orderBy('nama_lengkap')->get();

                                // Group by hierarchy: Entitas > Departemen > Divisi
                                $hierarchyGroups = $karyawanList->groupBy(function ($karyawan) {
                                    return $karyawan->entitas ? $karyawan->entitas->nama : 'Tanpa Entitas';
                                })->map(function ($entitasGroup) {
                                    return $entitasGroup->groupBy(function ($karyawan) {
                                        return $karyawan->departemen ? $karyawan->departemen->nama_departemen : 'Tanpa Departemen';
                                    })->map(function ($departemenGroup) {
                                        return $departemenGroup->groupBy(function ($karyawan) {
                                            return $karyawan->divisi ? $karyawan->divisi->nama_divisi : 'Tanpa Divisi';
                                        });
                                    });
                                });

                                // Handle selected karyawan (could be JSON string)
                                $selectedKaryawan = $get('karyawan') ?? [];
                                if (is_string($selectedKaryawan)) {
                                    $selectedKaryawan = json_decode($selectedKaryawan, true) ?? [];
                                }

                                return [
                                    'hierarchyGroups' => $hierarchyGroups,
                                    'selectedKaryawan' => $selectedKaryawan,
                                    'entitasId' => $entitasId
                                ];
                            }),

                        // Hidden field to store selected karyawan
                        Hidden::make('karyawan')
                            ->default('[]')
                            ->required()
                            ->dehydrateStateUsing(function ($state) {
                                // Convert JSON string back to array if needed
                                if (is_string($state)) {
                                    $decoded = json_decode($state, true);
                                    return is_array($decoded) ? $decoded : [];
                                }
                                return is_array($state) ? $state : [];
                            }),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nama_jadwal')
                    ->label('Nama Jadwal')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('tanggal_mulai')
                    ->label('Tanggal Mulai')
                    ->date('d M Y')
                    ->sortable(),

                TextColumn::make('tanggal_selesai')
                    ->label('Tanggal Selesai')
                    ->date('d M Y')
                    ->sortable(),

                TextColumn::make('shift.nama_shift')
                    ->label('Shift')
                    ->getStateUsing(function (JadwalMasal $record) {
                        $shift = $record->shift;
                        $shiftInfo = $shift->nama_shift . ' (' .
                            $shift->waktu_mulai->format('H:i') . ' - ' .
                            $shift->waktu_selesai->format('H:i') . ')';

                        if ($shift->waktu_mulai_periode2 && $shift->waktu_selesai_periode2) {
                            $shiftInfo .= ' | P2: ' .
                                $shift->waktu_mulai_periode2->format('H:i') . ' - ' .
                                $shift->waktu_selesai_periode2->format('H:i');
                        }

                        return $shiftInfo;
                    })
                    ->searchable()
                    ->sortable(),

                TextColumn::make('karyawan_count')
                    ->label('Jumlah Karyawan')
                    ->counts('karyawan')
                    ->sortable(),

                TextColumn::make('generation_status')
                    ->label('Status Generate')
                    ->getStateUsing(function (JadwalMasal $record) {
                        return $record->getGenerationStatusText();
                    })
                    ->badge()
                    ->color(fn(JadwalMasal $record) => $record->isGenerated() ? 'success' : 'warning')
                    ->icon(fn(JadwalMasal $record) => $record->isGenerated() ? 'heroicon-o-check-circle' : 'heroicon-o-clock')
                    ->sortable(query: function ($query, string $direction) {
                        return $query->orderBy('generated_at', $direction);
                    }),

                TextColumn::make('entitas.nama')
                    ->label('Entitas')
                    ->getStateUsing(function (JadwalMasal $record) {
                        // Use entitas_id from jadwal_masal directly
                        if ($record->entitas) {
                            return $record->entitas->nama;
                        }

                        // Fallback: get from assigned karyawan
                        $entitasIds = $record->karyawan()
                            ->whereNotNull('id_entitas')
                            ->distinct('id_entitas')
                            ->pluck('id_entitas');

                        if ($entitasIds->isEmpty()) {
                            return 'Tidak ada entitas';
                        }

                        $entitasNames = Entitas::whereIn('id', $entitasIds)
                            ->pluck('nama')
                            ->take(2);

                        if ($entitasIds->count() > 2) {
                            return $entitasNames->join(', ') . ' +' . ($entitasIds->count() - 2) . ' lainnya';
                        }

                        return $entitasNames->join(', ');
                    })
                    ->badge()
                    ->color('info')
                    ->searchable(false),

                TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime('d M Y H:i')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                // duplikasi
                Tables\Actions\Action::make('duplicate')
                    ->label('Duplikasi')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('info')
                    ->form([
                        DatePicker::make('tanggal_mulai')
                            ->label('Tanggal Mulai')
                            ->required()
                            ->default(Carbon::tomorrow())
                            ->minDate(Carbon::today()),
                        DatePicker::make('tanggal_selesai')
                            ->label('Tanggal Selesai')
                            ->required()
                            ->default(Carbon::tomorrow()->addDays(6))
                            ->minDate(fn(Forms\Get $get) => $get('tanggal_mulai')),
                    ])
                    ->action(function (JadwalMasal $record, array $data) {
                        try {
                            $duplicatedJadwal = $record->duplicate($data['tanggal_mulai'], $data['tanggal_selesai']);

                            $karyawanCount = $duplicatedJadwal->karyawan()->count();
                            $startDate = Carbon::parse($data['tanggal_mulai'])->format('d M Y');
                            $endDate = Carbon::parse($data['tanggal_selesai'])->format('d M Y');

                            Notification::make()
                                ->title('Jadwal masal berhasil diduplikasi!')
                                ->body("✅ Jadwal '{$duplicatedJadwal->nama_jadwal}' berhasil dibuat\n📅 Periode: {$startDate} - {$endDate}\n👥 {$karyawanCount} karyawan ditugaskan")
                                ->success()
                                ->duration(8000)
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Error saat duplikasi jadwal')
                                ->body('Error: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Duplikasi Jadwal Masal')
                    ->modalDescription('Apakah Anda yakin ingin menduplikasi jadwal ini? Jadwal baru akan dibuat dengan periode tanggal yang Anda tentukan.'),

                Tables\Actions\Action::make('generate')
                    ->label('Generate Jadwal')
                    ->icon('heroicon-o-play-circle')
                    ->color('success')
                    ->visible(function (JadwalMasal $record) {
                        $user = Auth::user();
                        $allowedRoles = ['admin', 'manager', 'supervisor', 'keptok'];
                        $allowedShieldRoles = ['manager_hrd', 'super_admin', 'hrd_manager'];

                        return (in_array($user->role, $allowedRoles) || $user->hasAnyRole($allowedShieldRoles))
                            && !$record->isGenerated();
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Generate Jadwal Individual')
                    ->modalDescription('Apakah Anda yakin ingin membuat jadwal individual untuk semua karyawan dalam periode ini? Setelah di-generate, jadwal masal ini tidak bisa di-generate ulang.')
                    ->modalSubmitActionLabel('Ya, Generate')
                    ->action(function (JadwalMasal $record) {
                        try {
                            $result = $record->generateSchedules();

                            Notification::make()
                                ->title('Jadwal berhasil digenerate!')
                                ->body("✅ {$result['generated']} jadwal baru dibuat\n⚠️ {$result['skipped']} jadwal sudah ada\n📅 {$result['total_days']} hari untuk {$result['total_employees']} karyawan")
                                ->success()
                                ->duration(8000)
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Error saat generate jadwal')
                                ->body('Error: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJadwalMasals::route('/'),
            'create' => Pages\CreateJadwalMasal::route('/create'),
            'edit' => Pages\EditJadwalMasal::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->with([
                'shift:id,nama_shift,waktu_mulai,waktu_selesai,is_split_shift,waktu_mulai_periode2,waktu_selesai_periode2',
                'creator:id,name',
                'karyawan:id,nama_lengkap'
            ]);

        $user = Auth::user();
        $query->where('created_by', $user->id);


        return $query;
    }
}
