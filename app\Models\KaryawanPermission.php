<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class KaryawanPermission extends Model
{
    use HasFactory;

    protected $fillable = [
        'karyawan_id',
        'permission_type',
        'scope_type',
        'scope_values',
        'is_active',
        'description',
        'created_by',
    ];

    protected $casts = [
        'scope_values' => 'array',
        'is_active' => 'boolean',
    ];

    // Permission types constants
    const PERMISSION_TYPES = [
        'approve_cuti' => 'Approve Cuti/Izin/Sakit',
        'view_absensi' => 'Lihat Absensi',
        'manage_jadwal' => 'Kelola Jadwal',
        'view_payroll' => 'Lihat Payroll',
        'manage_karyawan' => 'Kelola Data Karyawan',
        'view_reports' => 'Lihat Laporan',
        'manage_overtime' => 'Kelola <PERSON>',
        'approve_overtime' => 'Approve Lembur',
    ];

    // Scope types constants
    const SCOPE_TYPES = [
        'all' => 'Semua Data',
        'entitas' => 'Berdasarkan Entitas',
        'departemen' => 'Berdasarkan Departemen',
        'divisi' => 'Berdasarkan Divisi',
        'custom' => 'Custom (Pilih Karyawan)',
    ];

    /**
     * Relationship dengan karyawan
     */
    public function karyawan(): BelongsTo
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * Relationship dengan user yang membuat permission
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get permission type label
     */
    public function getPermissionTypeLabelAttribute(): string
    {
        return self::PERMISSION_TYPES[$this->permission_type] ?? $this->permission_type;
    }

    /**
     * Get scope type label
     */
    public function getScopeTypeLabelAttribute(): string
    {
        return self::SCOPE_TYPES[$this->scope_type] ?? $this->scope_type;
    }

    /**
     * Get scope description
     */
    public function getScopeDescriptionAttribute(): string
    {
        switch ($this->scope_type) {
            case 'all':
                return 'Semua data dalam sistem';

            case 'entitas':
                $scopeValues = is_array($this->scope_values) ? $this->scope_values : [];
                if (empty($scopeValues)) return 'Tidak ada entitas dipilih';
                $entitas = Entitas::whereIn('id', $scopeValues)->pluck('nama')->toArray();
                return 'Entitas: ' . implode(', ', $entitas);

            case 'departemen':
                $scopeValues = is_array($this->scope_values) ? $this->scope_values : [];
                if (empty($scopeValues)) return 'Tidak ada departemen dipilih';

                $departemenNames = [];
                foreach ($scopeValues as $scope) {
                    if (is_array($scope) && isset($scope['departemen_id'], $scope['entitas_id'])) {
                        $dept = Departemen::find($scope['departemen_id']);
                        $entitas = Entitas::find($scope['entitas_id']);
                        if ($dept && $entitas) {
                            $departemenNames[] = "{$dept->nama_departemen} ({$entitas->nama})";
                        }
                    }
                }
                return !empty($departemenNames) ? 'Departemen: ' . implode(', ', $departemenNames) : 'Departemen tidak valid';

            case 'divisi':
                $scopeValues = is_array($this->scope_values) ? $this->scope_values : [];
                if (empty($scopeValues)) return 'Tidak ada divisi dipilih';

                $divisiNames = [];
                foreach ($scopeValues as $scope) {
                    // Handle both old format (array with context) and new format (simple array)
                    if (is_array($scope) && isset($scope['divisi_id'], $scope['departemen_id'])) {
                        // Old format with context
                        $divisi = Divisi::find($scope['divisi_id']);
                        $dept = Departemen::find($scope['departemen_id']);
                        $entitas = isset($scope['entitas_id']) ? Entitas::find($scope['entitas_id']) : null;

                        if ($divisi && $dept) {
                            $name = "{$divisi->nama_divisi} ({$dept->nama_departemen}";
                            if ($entitas) {
                                $name .= " - {$entitas->nama}";
                            }
                            $name .= ")";
                            $divisiNames[] = $name;
                        }
                    } elseif (is_numeric($scope)) {
                        // New format (simple divisi ID)
                        $divisi = Divisi::with(['departemen.entitas'])->find($scope);
                        if ($divisi && $divisi->departemen) {
                            $name = "{$divisi->nama_divisi} ({$divisi->departemen->nama_departemen}";
                            if ($divisi->departemen->entitas) {
                                $name .= " - {$divisi->departemen->entitas->nama}";
                            }
                            $name .= ")";
                            $divisiNames[] = $name;
                        }
                    }
                }
                return !empty($divisiNames) ? 'Divisi: ' . implode(', ', $divisiNames) : 'Divisi tidak valid';

            case 'custom':
                $scopeValues = is_array($this->scope_values) ? $this->scope_values : [];
                if (empty($scopeValues)) return 'Tidak ada karyawan dipilih';
                $karyawan = Karyawan::whereIn('id', $scopeValues)->pluck('nama_lengkap')->toArray();
                $count = count($karyawan);
                if ($count <= 3) {
                    return 'Karyawan: ' . implode(', ', $karyawan);
                } else {
                    return 'Karyawan: ' . implode(', ', array_slice($karyawan, 0, 3)) . " dan {$count} lainnya";
                }

            default:
                return 'Scope tidak dikenal';
        }
    }

    /**
     * Check if user has permission for specific data
     */
    public static function hasPermission(int $karyawanId, string $permissionType, $targetData = null): bool
    {
        $permissions = self::where('karyawan_id', $karyawanId)
            ->where('permission_type', $permissionType)
            ->where('is_active', true)
            ->get();

        if ($permissions->isEmpty()) {
            return false;
        }

        foreach ($permissions as $permission) {
            if (self::checkScopePermission($permission, $targetData)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check scope permission
     */
    private static function checkScopePermission(KaryawanPermission $permission, $targetData): bool
    {
        $scopeValues = is_array($permission->scope_values) ? $permission->scope_values : [];

        switch ($permission->scope_type) {
            case 'all':
                return true;

            case 'entitas':
                if (!$targetData || !isset($targetData->id_entitas)) return false;
                return in_array($targetData->id_entitas, $scopeValues);

            case 'departemen':
                if (!$targetData || !isset($targetData->id_departemen)) return false;
                return in_array($targetData->id_departemen, $scopeValues);

            case 'divisi':
                if (!$targetData || !isset($targetData->id_divisi)) return false;
                return in_array($targetData->id_divisi, $scopeValues);

            case 'custom':
                if (!$targetData || !isset($targetData->id)) return false;
                return in_array($targetData->id, $scopeValues);

            default:
                return false;
        }
    }

    /**
     * Get accessible data IDs for a permission type
     */
    public static function getAccessibleDataIds(int $karyawanId, string $permissionType, string $dataType = 'karyawan'): array
    {
        $permissions = self::where('karyawan_id', $karyawanId)
            ->where('permission_type', $permissionType)
            ->where('is_active', true)
            ->get();

        if ($permissions->isEmpty()) {
            return [];
        }

        $accessibleIds = [];

        foreach ($permissions as $permission) {
            $scopeValues = is_array($permission->scope_values) ? $permission->scope_values : [];

            switch ($permission->scope_type) {
                case 'all':
                    // Return all active karyawan IDs
                    return Karyawan::where('status_aktif', true)->pluck('id')->toArray();

                case 'entitas':
                    if (!empty($scopeValues)) {
                        $ids = Karyawan::whereIn('id_entitas', $scopeValues)
                            ->where('status_aktif', true)
                            ->pluck('id')
                            ->toArray();
                        $accessibleIds = array_merge($accessibleIds, $ids);
                    }
                    break;

                case 'departemen':
                    if (!empty($scopeValues)) {
                        foreach ($scopeValues as $scope) {
                            if (is_array($scope) && isset($scope['departemen_id'], $scope['entitas_id'])) {
                                $ids = Karyawan::where('id_departemen', $scope['departemen_id'])
                                    ->where('id_entitas', $scope['entitas_id'])
                                    ->where('status_aktif', true)
                                    ->pluck('id')
                                    ->toArray();
                                $accessibleIds = array_merge($accessibleIds, $ids);
                            }
                        }
                    }
                    break;

                case 'divisi':
                    if (!empty($scopeValues)) {
                        foreach ($scopeValues as $scope) {
                            if (is_array($scope) && isset($scope['divisi_id'], $scope['departemen_id'])) {
                                $ids = Karyawan::where('id_divisi', $scope['divisi_id'])
                                    ->where('id_departemen', $scope['departemen_id'])
                                    ->where('status_aktif', true)
                                    ->pluck('id')
                                    ->toArray();
                                $accessibleIds = array_merge($accessibleIds, $ids);
                            }
                        }
                    }
                    break;

                case 'custom':
                    if (!empty($scopeValues)) {
                        $accessibleIds = array_merge($accessibleIds, $scopeValues);
                    }
                    break;
            }
        }

        return array_unique($accessibleIds);
    }
}
