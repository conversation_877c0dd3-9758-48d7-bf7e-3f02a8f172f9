<?php

namespace App\Filament\Resources;

use App\Models\KaryawanPermission;
use App\Models\Karyawan;
use App\Models\Entitas;
use App\Models\Departemen;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class KaryawanPermissionResource extends Resource
{
    protected static ?string $model = KaryawanPermission::class;

    protected static ?string $navigationIcon = 'heroicon-o-key';
    protected static ?string $navigationLabel = 'Permission Management';
    protected static ?string $modelLabel = 'Permission Karyawan';
    protected static ?string $pluralModelLabel = 'Permission Management';
    protected static ?string $navigationGroup = 'HR Management';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Permission')
                    ->schema([
                        Forms\Components\Select::make('karyawan_id')
                            ->label('Karyawan')
                            ->relationship('karyawan', 'nama_lengkap')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->getOptionLabelFromRecordUsing(fn($record) => "{$record->nama_lengkap} - {$record->nip}"),

                        Forms\Components\Select::make('permission_type')
                            ->label('Jenis Permission')
                            ->options(KaryawanPermission::PERMISSION_TYPES)
                            ->required()
                            ->live(),

                        Forms\Components\Select::make('scope_type')
                            ->label('Ruang Lingkup')
                            ->options(KaryawanPermission::SCOPE_TYPES)
                            ->required()
                            ->live()
                            ->afterStateUpdated(fn(Forms\Set $set) => $set('scope_values', null)),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi/Catatan')
                            ->placeholder('Catatan tambahan untuk permission ini...')
                            ->rows(3),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Status Aktif')
                            ->default(true),
                    ]),

                Forms\Components\Section::make('Pengaturan Ruang Lingkup')
                    ->schema([
                        Forms\Components\CheckboxList::make('scope_values_entitas')
                            ->label('Pilih Entitas')
                            ->options(fn() => Entitas::pluck('nama', 'id')->toArray())
                            ->columns(2)
                            ->visible(fn(Forms\Get $get) => $get('scope_type') === 'entitas')
                            ->default(function (?Model $record) {
                                return $record && $record->scope_type === 'entitas' && is_array($record->scope_values)
                                    ? $record->scope_values
                                    : [];
                            }),

                        Forms\Components\CheckboxList::make('scope_values_departemen')
                            ->label('Pilih Departemen')
                            ->options(fn() => Departemen::pluck('nama_departemen', 'id')->toArray())
                            ->columns(2)
                            ->visible(fn(Forms\Get $get) => $get('scope_type') === 'departemen')
                            ->default(function (?Model $record) {
                                return $record && $record->scope_type === 'departemen' && is_array($record->scope_values)
                                    ? $record->scope_values
                                    : [];
                            }),

                        Forms\Components\Group::make()
                            ->schema(function () {
                                $components = [];
                                $departemen = \App\Models\Departemen::with('divisi')->get();

                                foreach ($departemen as $dept) {
                                    if ($dept->divisi->isNotEmpty()) {
                                        $components[] = Forms\Components\Fieldset::make($dept->nama_departemen)
                                            ->schema([
                                                Forms\Components\CheckboxList::make("divisi_dept_{$dept->id}")
                                                    ->label('')
                                                    ->options($dept->divisi->pluck('nama_divisi', 'id')->toArray())
                                                    ->columns(2)
                                                    ->default(function (?Model $record) use ($dept) {
                                                        if (!$record || $record->scope_type !== 'divisi' || !is_array($record->scope_values)) {
                                                            return [];
                                                        }

                                                        // Filter only divisi that belong to this department
                                                        $deptDivisiIds = $dept->divisi->pluck('id')->toArray();
                                                        return array_intersect($record->scope_values, $deptDivisiIds);
                                                    })
                                                    ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                                        // Combine all department divisi selections into scope_values_divisi
                                                        $allSelected = [];
                                                        $departemen = \App\Models\Departemen::with('divisi')->get();

                                                        foreach ($departemen as $d) {
                                                            $deptSelected = $get("divisi_dept_{$d->id}") ?? [];
                                                            $allSelected = array_merge($allSelected, $deptSelected);
                                                        }

                                                        $set('scope_values_divisi', $allSelected);
                                                    })
                                                    ->live(),
                                            ]);
                                    }
                                }

                                return $components;
                            })
                            ->visible(fn(Forms\Get $get) => $get('scope_type') === 'divisi'),

                        Forms\Components\Hidden::make('scope_values_divisi')
                            ->default(function (?Model $record) {
                                return $record && $record->scope_type === 'divisi' && is_array($record->scope_values)
                                    ? $record->scope_values
                                    : [];
                            }),

                        Forms\Components\Select::make('scope_values_custom')
                            ->label('Pilih Karyawan')
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->options(fn() => Karyawan::where('status_aktif', true)
                                ->get()
                                ->mapWithKeys(fn($k) => [$k->id => "{$k->nama_lengkap} - {$k->nip}"])
                                ->toArray())
                            ->visible(fn(Forms\Get $get) => $get('scope_type') === 'custom')
                            ->default(function (?Model $record) {
                                return $record && $record->scope_type === 'custom' && is_array($record->scope_values)
                                    ? $record->scope_values
                                    : [];
                            }),

                        Forms\Components\Placeholder::make('scope_info')
                            ->label('Informasi')
                            ->content('Permission ini akan berlaku untuk semua data dalam sistem.')
                            ->visible(fn(Forms\Get $get) => $get('scope_type') === 'all'),
                    ])
                    ->visible(fn(Forms\Get $get) => filled($get('scope_type'))),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->label('Karyawan')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('karyawan.nip')
                    ->label('NIP')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\BadgeColumn::make('permission_type_label')
                    ->label('Jenis Permission')
                    ->colors([
                        'success' => 'approve_cuti',
                        'info' => 'view_absensi',
                        'warning' => 'manage_jadwal',
                        'danger' => 'manage_karyawan',
                        'primary' => 'view_payroll',
                    ]),

                Tables\Columns\BadgeColumn::make('scope_type_label')
                    ->label('Ruang Lingkup')
                    ->colors([
                        'success' => 'all',
                        'info' => 'entitas',
                        'warning' => 'departemen',
                        'primary' => 'divisi',
                        'gray' => 'custom',
                    ]),

                Tables\Columns\TextColumn::make('scope_description')
                    ->label('Detail Scope')
                    ->wrap()
                    ->limit(50),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Dibuat Oleh')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('permission_type')
                    ->label('Jenis Permission')
                    ->options(KaryawanPermission::PERMISSION_TYPES),

                Tables\Filters\SelectFilter::make('scope_type')
                    ->label('Ruang Lingkup')
                    ->options(KaryawanPermission::SCOPE_TYPES),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif')
                    ->placeholder('Semua')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),

                Tables\Filters\SelectFilter::make('karyawan')
                    ->relationship('karyawan', 'nama_lengkap')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),

                Tables\Actions\Action::make('duplicate')
                    ->label('Duplikasi')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('info')
                    ->form([
                        Forms\Components\Select::make('new_permission_type')
                            ->label('Jenis Permission Baru')
                            ->options(KaryawanPermission::PERMISSION_TYPES)
                            ->required()
                            ->helperText('Pilih jenis permission untuk duplikasi ini'),

                        Forms\Components\Select::make('new_karyawan_id')
                            ->label('Karyawan Baru (Opsional)')
                            ->relationship('karyawan', 'nama_lengkap')
                            ->searchable()
                            ->preload()
                            ->getOptionLabelFromRecordUsing(fn($record) => "{$record->nama_lengkap} - {$record->nip}")
                            ->helperText('Kosongkan jika ingin menggunakan karyawan yang sama'),

                        Forms\Components\Textarea::make('new_description')
                            ->label('Deskripsi Baru (Opsional)')
                            ->placeholder('Deskripsi untuk permission yang diduplikasi...')
                            ->rows(3)
                            ->helperText('Kosongkan jika ingin menggunakan deskripsi yang sama'),
                    ])
                    ->action(function (array $data, $record) {
                        try {
                            // Buat duplikasi permission dengan data baru
                            $newPermission = $record->replicate();

                            // Update field yang baru
                            $newPermission->permission_type = $data['new_permission_type'];

                            // Tentukan karyawan dan dapatkan nama karyawan
                            if (isset($data['new_karyawan_id']) && !empty($data['new_karyawan_id'])) {
                                // Jika ada karyawan baru yang dipilih
                                $karyawan = \App\Models\Karyawan::find($data['new_karyawan_id']);
                                if (!$karyawan) {
                                    \Filament\Notifications\Notification::make()
                                        ->title('Duplikasi Gagal')
                                        ->body('Karyawan yang dipilih tidak ditemukan.')
                                        ->danger()
                                        ->send();
                                    return;
                                }
                                $newPermission->karyawan_id = $data['new_karyawan_id'];
                                $karyawanName = $karyawan->nama_lengkap;
                            } else {
                                // Gunakan karyawan yang sama dengan record asli
                                $newPermission->karyawan_id = $record->karyawan_id;

                                // Dapatkan nama karyawan dengan pengecekan yang aman
                                if ($record->karyawan) {
                                    $karyawanName = $record->karyawan->nama_lengkap;
                                } else {
                                    // Jika relasi tidak ter-load, coba ambil dari database
                                    $karyawan = \App\Models\Karyawan::find($record->karyawan_id);
                                    $karyawanName = $karyawan ? $karyawan->nama_lengkap : 'Karyawan ID: ' . $record->karyawan_id;
                                }
                            }

                            $newPermission->description = !empty($data['new_description']) ? $data['new_description'] : $record->description;
                            $newPermission->created_by = auth()->id();
                            $newPermission->created_at = now();
                            $newPermission->updated_at = now();

                            $newPermission->save();

                            // Notifikasi sukses
                            \Filament\Notifications\Notification::make()
                                ->title('Permission Berhasil Diduplikasi')
                                ->body("Permission '{$data['new_permission_type']}' berhasil dibuat untuk {$karyawanName}")
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('Duplikasi Gagal')
                                ->body('Terjadi kesalahan saat menduplikasi permission: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->modalHeading('Duplikasi Permission')
                    ->modalDescription('Buat salinan permission ini dengan jenis permission yang berbeda')
                    ->modalSubmitActionLabel('Duplikasi')
                    ->modalCancelActionLabel('Batal'),

                Tables\Actions\Action::make('toggle_status')
                    ->label(fn($record) => $record->is_active ? 'Nonaktifkan' : 'Aktifkan')
                    ->icon(fn($record) => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn($record) => $record->is_active ? 'danger' : 'success')
                    ->action(function ($record) {
                        $record->is_active = !$record->is_active;
                        $record->save();
                    })
                    ->requiresConfirmation(),

                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('Aktifkan')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each(fn($record) => $record->update(['is_active' => true]));
                        }),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Nonaktifkan')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each(fn($record) => $record->update(['is_active' => false]));
                        }),

                    Tables\Actions\BulkAction::make('bulk_duplicate')
                        ->label('Duplikasi dengan Permission Baru')
                        ->icon('heroicon-o-document-duplicate')
                        ->color('info')
                        ->form([
                            Forms\Components\Select::make('new_permission_type')
                                ->label('Jenis Permission Baru')
                                ->options(KaryawanPermission::PERMISSION_TYPES)
                                ->required()
                                ->helperText('Pilih jenis permission untuk semua duplikasi'),

                            Forms\Components\Textarea::make('new_description')
                                ->label('Deskripsi Tambahan (Opsional)')
                                ->placeholder('Deskripsi tambahan untuk permission yang diduplikasi...')
                                ->rows(3)
                                ->helperText('Akan ditambahkan ke deskripsi asli'),
                        ])
                        ->action(function (array $data, $records) {
                            $count = 0;
                            $errors = 0;

                            foreach ($records as $record) {
                                try {
                                    // Buat duplikasi permission dengan data baru
                                    $newPermission = $record->replicate();

                                    // Update field yang baru
                                    $newPermission->permission_type = $data['new_permission_type'];

                                    // Pastikan karyawan_id tidak null
                                    if (!$record->karyawan_id) {
                                        // Skip record yang tidak memiliki karyawan_id
                                        $errors++;
                                        continue;
                                    }

                                    // Gunakan karyawan_id yang sama dengan record asli
                                    $newPermission->karyawan_id = $record->karyawan_id;

                                    // Tambahkan deskripsi baru jika ada
                                    if (!empty($data['new_description'])) {
                                        $newPermission->description = ($record->description ? $record->description . "\n\n" : '') .
                                            $data['new_description'];
                                    }

                                    $newPermission->created_by = auth()->id();
                                    $newPermission->created_at = now();
                                    $newPermission->updated_at = now();

                                    $newPermission->save();
                                    $count++;
                                } catch (\Exception $exception) {
                                    $errors++;
                                    // Log error untuk debugging jika diperlukan
                                    Log::error('Bulk duplicate permission error: ' . $exception->getMessage());
                                }
                            }

                            // Notifikasi hasil
                            if ($count > 0) {
                                $message = "{$count} permission berhasil diduplikasi dengan jenis '{$data['new_permission_type']}'";
                                if ($errors > 0) {
                                    $message .= ". {$errors} permission gagal diduplikasi karena data tidak valid.";
                                }

                                \Filament\Notifications\Notification::make()
                                    ->title('Duplikasi Permission Selesai')
                                    ->body($message)
                                    ->success()
                                    ->send();
                            } else {
                                \Filament\Notifications\Notification::make()
                                    ->title('Duplikasi Gagal')
                                    ->body('Tidak ada permission yang berhasil diduplikasi. Pastikan data permission valid.')
                                    ->danger()
                                    ->send();
                            }
                        })
                        ->deselectRecordsAfterCompletion()
                        ->modalHeading('Duplikasi Multiple Permission')
                        ->modalDescription('Buat salinan dari semua permission yang dipilih dengan jenis permission yang baru')
                        ->modalSubmitActionLabel('Duplikasi Semua')
                        ->modalCancelActionLabel('Batal'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => \App\Filament\Resources\KaryawanPermissionResource\Pages\ListKaryawanPermissions::route('/'),
            'create' => \App\Filament\Resources\KaryawanPermissionResource\Pages\CreateKaryawanPermission::route('/create'),
            'view' => \App\Filament\Resources\KaryawanPermissionResource\Pages\ViewKaryawanPermission::route('/{record}'),
            'edit' => \App\Filament\Resources\KaryawanPermissionResource\Pages\EditKaryawanPermission::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)->count();
    }
}
