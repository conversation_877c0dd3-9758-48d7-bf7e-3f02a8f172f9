<?php

namespace App\Filament\Akunting\Resources\DailyTransactionResource\Pages;

use App\Filament\Akunting\Resources\DailyTransactionResource;
use App\Models\DailyTransaction;
use App\Models\TransactionSubcategory;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Get;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ListDailyTransactions extends ListRecords
{
    protected static string $resource = DailyTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Tambah Transaksi')
                ->icon('heroicon-o-plus')
                ->color('primary')
                ->size('lg')
                ->modalHeading('Tambah Transaksi Baru')
                ->modalDescription('Isi form di bawah untuk menambahkan transaksi harian baru.')
                ->modalSubmitActionLabel('Simpan Transaksi')
                ->modalCancelActionLabel('Batal')
                ->successNotificationTitle('Transaksi berhasil ditambahkan!')
                ->createAnother(false)
                ->slideOver()
                ->form([
                    Forms\Components\Select::make('outlet_id')
                        ->relationship('outlet', 'name')
                        ->required()
                        ->searchable()
                        ->preload()
                        ->label('Outlet'),

                    Forms\Components\DatePicker::make('transaction_date')
                        ->required()
                        ->default(now())
                        ->label('Tanggal Transaksi'),

                    Forms\Components\TextInput::make('description')
                        ->required()
                        ->maxLength(255)
                        ->label('Deskripsi'),

                    Forms\Components\Select::make('type')
                        ->options([
                            'income' => 'Pemasukan',
                            'expense' => 'Pengeluaran',
                            'revenue' => 'Pendapatan',
                            'receivable' => 'Piutang',
                            'cash_deficit' => 'Kekurangan Kas',
                        ])
                        ->required()
                        ->live()
                        ->label('Jenis Transaksi'),

                    Forms\Components\Select::make('income_category_id')
                        ->relationship('incomeCategoryModel', 'name')
                        ->searchable()
                        ->preload()
                        ->visible(fn(Get $get): bool => $get('type') === 'income')
                        ->label('Kategori Pemasukan'),

                    Forms\Components\Select::make('expense_category_id')
                        ->relationship('expenseCategoryModel', 'name')
                        ->searchable()
                        ->preload()
                        ->live()
                        ->visible(fn(Get $get): bool => $get('type') === 'expense')
                        ->label('Kategori Pengeluaran'),

                    Forms\Components\Select::make('subcategory_id')
                        ->options(function (Get $get) {
                            $expenseCategoryId = $get('expense_category_id');
                            if (!$expenseCategoryId) {
                                return [];
                            }
                            return TransactionSubcategory::where('expense_category_id', $expenseCategoryId)
                                ->pluck('name', 'id');
                        })
                        ->searchable()
                        ->visible(fn(Get $get): bool => $get('type') === 'expense' && $get('expense_category_id'))
                        ->label('Sub Kategori'),

                    Forms\Components\TextInput::make('amount')
                        ->required()
                        ->numeric()
                        ->prefix('Rp')
                        ->label('Jumlah'),

                    Forms\Components\Textarea::make('notes')
                        ->maxLength(500)
                        ->label('Catatan')
                        ->rows(3),
                ])
                ->mutateFormDataUsing(function (array $data): array {
                    $data['created_by'] = auth()->id();
                    return $data;
                }),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('Semua')
                ->badge(DailyTransaction::count())
                ->badgeColor('primary'),
            'revenue' => Tab::make('Pendapatan')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('type', 'revenue'))
                ->badge(DailyTransaction::where('type', 'revenue')->count())
                ->badgeColor('success'),
            'expense' => Tab::make('Pengeluaran')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('type', 'expense'))
                ->badge(DailyTransaction::where('type', 'expense')->count())
                ->badgeColor('danger'),
            'receivable' => Tab::make('Piutang')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('type', 'receivable'))
                ->badge(DailyTransaction::where('type', 'receivable')->count())
                ->badgeColor('warning'),
            'cash_deficit' => Tab::make('Kekurangan Kas')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('type', 'cash_deficit'))
                ->badge(DailyTransaction::where('type', 'cash_deficit')->count())
                ->badgeColor('gray'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            DailyTransactionResource\Widgets\TransactionStatsWidget::class,
        ];
    }
}
