<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="dashboard-container">
        <div class="space-y-6">
            
            <div class="bg-white border border-gray-100 shadow-md dark:bg-gray-900 rounded-3xl dark:border-gray-700">
                
                <div
                    class="px-8 py-6 bg-blue-300 border-b border-gray-100 rounded-t-3xl bg-gradient-to-br from-blue-300 via-blue-300 to-indigo-600 dark:from-gray-800 dark:to-gray-700 dark:border-gray-600">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div
                                class="flex items-center justify-center w-16 h-16 bg-white shadow-lg dark:bg-gray-700 rounded-2xl">
                                <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                    </path>
                                </svg>
                            </div>
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                                    Dashboard Supervisor
                                </h1>
                                <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
                                    Monitoring dan laporan berdasarkan permission karyawan
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div
                                class="px-4 py-3 text-right bg-white border border-gray-100 shadow-sm dark:bg-gray-700 dark:border-gray-600 rounded-xl">
                                <div
                                    class="text-xs font-semibold tracking-wide uppercase text-blue-600 dark:text-blue-400">
                                    Status
                                </div>
                                <div class="text-lg font-bold text-gray-900 dark:text-white">
                                    Aktif
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    <?php echo e(now()->format('d M Y H:i')); ?>

                                </div>
                            </div>
                            <button onclick="window.location.reload()"
                                class="inline-flex items-center px-6 py-3 text-sm font-bold text-white transition-all duration-300 transform border border-transparent shadow-lg bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 hover:scale-105">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                    </path>
                                </svg>
                                Refresh Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            
            <div class="space-y-6">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getWidgets(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $widget): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split($widget, ['lazy' => false]);

$__html = app('livewire')->mount($__name, $__params, $widget, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/pages/supervisor-dashboard.blade.php ENDPATH**/ ?>