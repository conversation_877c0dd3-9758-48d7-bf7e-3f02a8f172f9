<div>
    @if ($getRecord()->approvalStatus)
        <p class="px-3">
            <small>
                @if ($getRecord()->isApprovalCompleted())
                    <div class="flex gap-x-4 ml-auto">
                        <span
                            class="px-3 py-1  rounded-full text-xs
                    @if ($getRecord()->lastApproval->approval_action == 'Approved') bg-green-500 text-white-800 @endif
                    @if ($getRecord()->lastApproval->approval_action == 'Discarded') bg-red-500 text-white @endif
                    @if ($getRecord()->lastApproval->approval_action == 'Rejected') bg-red-500 text-white @endif
                    @if ($getRecord()->lastApproval->approval_action == 'Discarded') bg-danger-500 text-white @endif
                    @if ($getRecord()->lastApproval->approval_action == 'Pending') bg-warning-500 text-yellow-800 @endif
                    @if ($getRecord()->lastApproval->approval_action == 'Submitted') bg-blue-500 text-white @endif">
                            {{ __('filament-approvals::approvals.actions.history.' . $getRecord()->lastApproval->approval_action) }}
                        </span>
                    </div>
                    {{-- {{ __('filament-approvals::approvals.status_column.approval_by_prefix') }}
                    @if ($getRecord()->lastApproval)
                        {{ $getRecord()->lastApproval->approver_name }}
                    @else
                        {{ $getRecord()->createdBy()->name }}
                    @endif --}}
                @else
                @if ($getRecord()->isSubmitted())
                <div class="flex gap-x-4 ml-auto">
                        <span
                            class="px-3 py-1  rounded-full text-xs
                    @if ($getRecord()->lastApproval->approval_action == 'Approved') bg-green-500 text-white-800 @endif
                    @if ($getRecord()->lastApproval->approval_action == 'Discarded') bg-red-500 text-white @endif
                    @if ($getRecord()->lastApproval->approval_action == 'Rejected') bg-red-500 text-white @endif
                    @if ($getRecord()->lastApproval->approval_action == 'Discarded') bg-danger-500 text-white @endif
                    @if ($getRecord()->lastApproval->approval_action == 'Pending') bg-warning-500 text-yellow-800 @endif
                    @if ($getRecord()->lastApproval->approval_action == 'Submitted') bg-blue-500 text-white @endif">
                            {{ __('filament-approvals::approvals.actions.history.' . $getRecord()->lastApproval->approval_action) }}
                        </span>
                    </div>
                @else
                {{ ('Belum Dikirim') }}
                @endif
                    {{-- {{ $getRecord()->approvalStatus->status }}
                    {{ __('filament-approvals::approvals.status_column.approval_by_prefix') }}
                    @if ($getRecord()->nextApprover)
                        {{ $getRecord()->nextApprover->name }}
                    @else
                        {{ $getRecord()->createdBy()->name }}
                    @endif --}}
                @endif
            </small>
        </p>
        {{-- <p class="px-3 text-xs">
            <small>
                {{ $getRecord()->isApprovalCompleted()
                    ? __('filament-approvals::approvals.status_column.approval_complete')
                    : __('filament-approvals::approvals.status_column.approval_in_process') }}
            </small>
        </p> --}}
    @else
        <span class="px-3 py-1 bg-gray-200 text-gray-800 rounded-full text-xs">
            {{ __('filament-approvals::approvals.status_column.approval_status_does_not_exist') }}
        </span>
    @endif
</div>
