<?php

namespace App\Filament\Akunting\Pages;

use Filament\Pages\Page;
use App\Models\Outlet;
use App\Models\DailyTransaction;

class TestReport extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Test Report';

    protected static ?string $title = 'Test Report';

    protected static ?string $navigationGroup = 'Pengaturan';

    protected static string $view = 'filament.akunting.pages.test-report';

    public function mount(): void
    {
        // Simple test
    }

    public function getOutletCount()
    {
        return Outlet::count();
    }

    public function getTransactionCount()
    {
        return DailyTransaction::count();
    }
}
