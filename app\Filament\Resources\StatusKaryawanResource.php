<?php

namespace App\Filament\Resources;

use App\Models\Karyawan;
use App\Models\CutiIzin;
use App\Models\Schedule;
use App\Models\Absensi;
use App\Services\PermissionService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class StatusKaryawanResource extends Resource
{
    protected static ?string $model = Karyawan::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Status Karyawan Hari Ini';
    protected static ?string $modelLabel = 'Status Karyawan';
    protected static ?string $pluralModelLabel = 'Status Karyawan Hari Ini';
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    protected static ?int $navigationSort = 1;


    public static function getEloquentQuery(): Builder
    {
        $user = Auth::user();

        // Super admin, direktur, and manager <PERSON><PERSON> can see all karyawan
        if ($user->hasRole(['super_admin', 'direktur', 'manager_hrd'])) {
            return parent::getEloquentQuery();
        }

        // Apply permission filtering for other users
        return PermissionService::applyPermissionFilter(
            parent::getEloquentQuery(),
            'view_absensi',
            'id'
        );
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena ini read-only resource
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('foto_profil')
                    ->label('Foto')
                    ->circular()
                    ->defaultImageUrl(url('/images/default-avatar.png'))
                    ->size(40),

                Tables\Columns\TextColumn::make('nama_lengkap')
                    ->label('Nama Karyawan')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('nip')
                    ->label('NIP')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('departemen.nama_departemen')
                    ->label('Departemen')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('divisi.nama_divisi')
                    ->label('Divisi')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('entitas.nama')
                    ->label('Entitas')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\BadgeColumn::make('status_hari_ini')
                    ->label('Status Hari Ini')
                    ->getStateUsing(function (Karyawan $record): string {
                        return self::getStatusHariIni($record);
                    })
                    ->colors([
                        'success' => 'hadir',
                        'warning' => 'terlambat',
                        'danger' => ['alpha', 'sakit'],
                        'info' => ['cuti', 'izin'],
                        'gray' => ['libur', 'tidak_ada_jadwal'],
                    ])
                    ->icons([
                        'heroicon-o-check-circle' => 'hadir',
                        'heroicon-o-clock' => 'terlambat',
                        'heroicon-o-x-circle' => 'alpha',
                        'heroicon-o-heart' => 'sakit',
                        'heroicon-o-calendar-days' => 'cuti',
                        'heroicon-o-document-text' => 'izin',
                        'heroicon-o-moon' => 'libur',
                        'heroicon-o-minus-circle' => 'tidak_ada_jadwal',
                    ]),

                Tables\Columns\TextColumn::make('keterangan_status')
                    ->label('Keterangan')
                    ->getStateUsing(function (Karyawan $record): string {
                        return self::getKeteranganStatus($record);
                    })
                    ->wrap()
                    ->limit(50),

                Tables\Columns\TextColumn::make('jadwal_hari_ini')
                    ->label('Jadwal')
                    ->getStateUsing(function (Karyawan $record): string {
                        $today = Carbon::today();
                        $schedule = Schedule::where('karyawan_id', $record->id)
                            ->whereDate('tanggal_jadwal', $today)
                            ->with('shift')
                            ->first();

                        if (!$schedule) {
                            return 'Tidak ada jadwal';
                        }

                        $shift = $schedule->shift;
                        if ($shift) {
                            return $shift->nama_shift . ' (' .
                                Carbon::parse($shift->waktu_mulai)->format('H:i') . ' - ' .
                                Carbon::parse($shift->waktu_selesai)->format('H:i') . ')';
                        }

                        return 'Jadwal tidak lengkap';
                    })
                    ->toggleable(),

                Tables\Columns\TextColumn::make('no_hp')
                    ->label('No. HP')
                    ->toggleable()
                    ->copyable()
                    ->copyMessage('Nomor HP disalin!')
                    ->copyMessageDuration(1500),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status_filter')
                    ->label('Filter Status')
                    ->options([
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                        'libur' => 'Libur/Tidak Ada Jadwal',
                        'hadir' => 'Hadir',
                        'terlambat' => 'Terlambat',
                        'alpha' => 'Alpha',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (!$data['value']) {
                            return $query;
                        }

                        $today = Carbon::today();
                        $status = $data['value'];

                        return $query->where(function ($q) use ($today, $status) {
                            $karyawanIds = collect();

                            if (in_array($status, ['cuti', 'izin', 'sakit'])) {
                                // Karyawan yang sedang cuti/izin/sakit
                                $cutiIds = CutiIzin::where('status', 'approved')
                                    ->where('jenis_permohonan', $status)
                                    ->whereDate('tanggal_mulai', '<=', $today)
                                    ->whereDate('tanggal_selesai', '>=', $today)
                                    ->pluck('karyawan_id');
                                $karyawanIds = $karyawanIds->merge($cutiIds);
                            } elseif ($status === 'libur') {
                                // Karyawan yang tidak ada jadwal
                                $scheduledIds = Schedule::whereDate('tanggal_jadwal', $today)
                                    ->pluck('karyawan_id');
                                $allIds = Karyawan::where('status_aktif', true)->pluck('id');
                                $karyawanIds = $allIds->diff($scheduledIds);
                            } elseif (in_array($status, ['hadir', 'terlambat', 'alpha'])) {
                                // Karyawan dengan status absensi tertentu
                                $absensiIds = Absensi::whereDate('tanggal_absensi', $today)
                                    ->where('status', $status)
                                    ->pluck('karyawan_id');
                                $karyawanIds = $karyawanIds->merge($absensiIds);
                            }

                            $q->whereIn('id', $karyawanIds);
                        });
                    }),

                Tables\Filters\SelectFilter::make('entitas')
                    ->relationship('entitas', 'nama')
                    ->label('Entitas')
                    ->preload(),

                Tables\Filters\SelectFilter::make('departemen')
                    ->relationship('departemen', 'nama_departemen')
                    ->label('Departemen')
                    ->preload(),

                Tables\Filters\SelectFilter::make('divisi')
                    ->relationship('divisi', 'nama_divisi')
                    ->label('Divisi')
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Detail')
                    ->modalHeading('Detail Status Karyawan')
                    ->modalContent(function (Karyawan $record) {
                        return view('filament.admin.modals.status-detail', [
                            'karyawan' => $record,
                            'statusInfo' => self::getDetailedStatusInfo($record)
                        ]);
                    }),
            ])
            ->bulkActions([
                // Tidak ada bulk actions untuk read-only resource
            ])
            ->defaultSort('nama_lengkap')
            ->poll('30s') // Auto refresh setiap 30 detik
            ->striped();
    }

    /**
     * Get status hari ini untuk karyawan
     */
    public static function getStatusHariIni(Karyawan $karyawan): string
    {
        // Check permission first
        if (!self::canViewKaryawanStatus($karyawan)) {
            return 'no_access';
        }

        $today = Carbon::today();

        // Cek apakah sedang cuti/izin/sakit
        $cutiIzin = CutiIzin::where('karyawan_id', $karyawan->id)
            ->where('status', 'approved')
            ->whereDate('tanggal_mulai', '<=', $today)
            ->whereDate('tanggal_selesai', '>=', $today)
            ->first();

        if ($cutiIzin) {
            return $cutiIzin->jenis_permohonan;
        }

        // Cek jadwal hari ini
        $schedule = Schedule::where('karyawan_id', $karyawan->id)
            ->whereDate('tanggal_jadwal', $today)
            ->first();

        if (!$schedule) {
            return 'tidak_ada_jadwal';
        }

        // Cek absensi hari ini
        $absensi = Absensi::where('karyawan_id', $karyawan->id)
            ->whereDate('tanggal_absensi', $today)
            ->first();

        if ($absensi) {
            return $absensi->status;
        }

        // Jika ada jadwal tapi belum absen, cek apakah sudah lewat waktu
        $now = Carbon::now();
        $shiftStart = Carbon::parse($schedule->waktu_masuk);

        if ($now->greaterThan($shiftStart->addHours(2))) {
            return 'alpha'; // Dianggap alpha jika sudah lewat 2 jam dari jadwal
        }

        return 'libur'; // Default
    }

    /**
     * Get keterangan status
     */
    public static function getKeteranganStatus(Karyawan $karyawan): string
    {
        $today = Carbon::today();

        // Cek cuti/izin/sakit
        $cutiIzin = CutiIzin::where('karyawan_id', $karyawan->id)
            ->where('status', 'approved')
            ->whereDate('tanggal_mulai', '<=', $today)
            ->whereDate('tanggal_selesai', '>=', $today)
            ->first();

        if ($cutiIzin) {
            $range = $cutiIzin->tanggal_mulai->format('d/m') . ' - ' . $cutiIzin->tanggal_selesai->format('d/m');
            return $cutiIzin->alasan . ' (' . $range . ')';
        }

        // Cek absensi
        $absensi = Absensi::where('karyawan_id', $karyawan->id)
            ->whereDate('tanggal_absensi', $today)
            ->first();

        if ($absensi && $absensi->keterangan) {
            return $absensi->keterangan;
        }

        // Cek jadwal
        $schedule = Schedule::where('karyawan_id', $karyawan->id)
            ->whereDate('tanggal_jadwal', $today)
            ->first();

        if (!$schedule) {
            return 'Tidak ada jadwal kerja hari ini';
        }

        return 'Menunggu absensi';
    }

    /**
     * Get detailed status info untuk modal
     */
    public static function getDetailedStatusInfo(Karyawan $karyawan): array
    {
        $today = Carbon::today();

        return [
            'status' => self::getStatusHariIni($karyawan),
            'keterangan' => self::getKeteranganStatus($karyawan),
            'jadwal' => Schedule::where('karyawan_id', $karyawan->id)
                ->whereDate('tanggal_jadwal', $today)
                ->with('shift')
                ->first(),
            'absensi' => Absensi::where('karyawan_id', $karyawan->id)
                ->whereDate('tanggal_absensi', $today)
                ->first(),
            'cutiIzin' => CutiIzin::where('karyawan_id', $karyawan->id)
                ->where('status', 'approved')
                ->whereDate('tanggal_mulai', '<=', $today)
                ->whereDate('tanggal_selesai', '>=', $today)
                ->first(),
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \App\Filament\Resources\StatusKaryawanResource\Pages\ListStatusKaryawan::route('/'),
        ];
    }
}
