<div>
    @foreach ($data as $a)
        <div class="p-4 bg-gray-50 mb-4 rounded border-1">
            <div class="flex items-center gap-x-3">
                <img src="{{ Filament\Facades\Filament::getUserAvatarUrl($a->user) }}"
                    class="h-6 w-6 flex-none rounded-full bg-gray-800" alt="Avatar" />
                <h3 class="flex truncate text-sm font-semibold leading-6 text-gray-500">
                    {{ $a->user->name }}
                </h3>
                <time class="flex-none text-xs text-gray-500">{{ $a->created_at->diffForHumans() }} -
                    {{ $a->created_at }}</time>
                <div class="flex gap-x-4 ml-auto">
                    <span
                        class="px-3 py-1  rounded-full text-xs
@php
$approvalAction = $a->approval_action ?? '';
    $statusClasses = [
        'Approved' => 'bg-green-500 text-white',
        'Rejected' => 'bg-red-500 text-white',
        'Discarded' => 'bg-red-500 text-white',
        'Pending' => 'bg-warning-500 text-yellow-800',
        'Submitted' => 'bg-blue-500 text-white',
    ];
    $classes = $statusClasses[$approvalAction] ?? 'bg-gray-500 text-white'; @endphp

class="{{ $classes }}">
                        {{ __('filament-approvals::approvals.actions.history.' . $a->approval_action) }}
                    </span>
                </div>
            </div>
            @if ($a->comment)
                <div class="mt-3 text-sm">{!! $a->comment !!}</div>
            @endif
        </div>
    @endforeach
</div>
