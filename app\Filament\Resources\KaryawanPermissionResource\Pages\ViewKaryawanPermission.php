<?php

namespace App\Filament\Resources\KaryawanPermissionResource\Pages;

use App\Filament\Resources\KaryawanPermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewKaryawanPermission extends ViewRecord
{
    protected static string $resource = KaryawanPermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informasi Karyawan')
                    ->schema([
                        Infolists\Components\TextEntry::make('karyawan.nama_lengkap')
                            ->label('Nama Karyawan')
                            ->weight('bold')
                            ->size('lg'),

                        Infolists\Components\TextEntry::make('karyawan.nip')
                            ->label('NIP')
                            ->copyable()
                            ->badge()
                            ->color('gray'),

                        Infolists\Components\TextEntry::make('karyawan_hierarchy')
                            ->label('Hirarki Organisasi')
                            ->getStateUsing(function ($record) {
                                $karyawan = $record->karyawan;
                                if (!$karyawan) return 'Data tidak tersedia';

                                $hierarchy = [];

                                // Entitas
                                if ($karyawan->entitas) {
                                    $hierarchy[] = "🏢 {$karyawan->entitas->nama}";
                                }

                                // Departemen
                                if ($karyawan->departemen) {
                                    $hierarchy[] = "🏛️ {$karyawan->departemen->nama_departemen}";
                                }

                                // Divisi
                                if ($karyawan->divisi) {
                                    $hierarchy[] = "🏗️ {$karyawan->divisi->nama_divisi}";
                                }

                                // Jabatan
                                if ($karyawan->jabatan) {
                                    $hierarchy[] = "👤 {$karyawan->jabatan->nama_jabatan}";
                                }

                                return !empty($hierarchy) ? implode(' → ', $hierarchy) : 'Hirarki tidak lengkap';
                            })
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Informasi Permission')
                    ->schema([
                        Infolists\Components\TextEntry::make('permission_type_label')
                            ->label('Jenis Permission')
                            ->badge()
                            ->color(function ($state) {
                                return match ($state) {
                                    'Approve Cuti/Izin/Sakit' => 'success',
                                    'Lihat Absensi' => 'info',
                                    'Kelola Jadwal' => 'warning',
                                    'Kelola Data Karyawan' => 'danger',
                                    'Lihat Payroll' => 'primary',
                                    default => 'gray',
                                };
                            }),

                        Infolists\Components\TextEntry::make('scope_type_label')
                            ->label('Ruang Lingkup')
                            ->badge()
                            ->color(function ($state) {
                                return match ($state) {
                                    'Semua data dalam sistem' => 'success',
                                    'Entitas tertentu' => 'info',
                                    'Departemen tertentu' => 'warning',
                                    'Divisi tertentu' => 'primary',
                                    'Karyawan tertentu' => 'gray',
                                    default => 'secondary',
                                };
                            }),

                        Infolists\Components\TextEntry::make('scope_description')
                            ->label('Detail Scope')
                            ->columnSpanFull(),

                        Infolists\Components\IconEntry::make('is_active')
                            ->label('Status Permission')
                            ->boolean()
                            ->trueIcon('heroicon-o-check-circle')
                            ->falseIcon('heroicon-o-x-circle')
                            ->trueColor('success')
                            ->falseColor('danger'),

                        Infolists\Components\TextEntry::make('description')
                            ->label('Deskripsi/Catatan')
                            ->placeholder('Tidak ada deskripsi')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Karyawan yang Terkena Dampak')
                    ->schema([
                        Infolists\Components\TextEntry::make('affected_employees')
                            ->label('Daftar Karyawan')
                            ->getStateUsing(function ($record) {
                                if ($record->scope_type === 'all') {
                                    return '✅ Semua karyawan aktif dalam sistem';
                                }

                                $accessibleIds = \App\Models\KaryawanPermission::getAccessibleDataIds(
                                    $record->karyawan_id,
                                    $record->permission_type
                                );

                                if (empty($accessibleIds)) {
                                    return '❌ Tidak ada karyawan yang dapat diakses';
                                }

                                $karyawanList = \App\Models\Karyawan::whereIn('id', $accessibleIds)
                                    ->with(['entitas', 'departemen', 'divisi', 'jabatan'])
                                    ->get()
                                    ->map(function ($k) {
                                        $info = [];
                                        $info[] = "👤 {$k->nama_lengkap} ({$k->nip})";

                                        $hierarchy = [];
                                        if ($k->entitas) $hierarchy[] = $k->entitas->nama;
                                        if ($k->departemen) $hierarchy[] = $k->departemen->nama_departemen;
                                        if ($k->divisi) $hierarchy[] = $k->divisi->nama_divisi;
                                        if ($k->jabatan) $hierarchy[] = $k->jabatan->nama_jabatan;

                                        if (!empty($hierarchy)) {
                                            $info[] = "   📍 " . implode(' → ', $hierarchy);
                                        }

                                        return implode("\n", $info);
                                    })
                                    ->toArray();

                                $count = count($karyawanList);
                                $preview = array_slice($karyawanList, 0, 10);
                                $result = implode("\n\n", $preview);

                                if ($count > 10) {
                                    $result .= "\n\n... dan " . ($count - 10) . " karyawan lainnya";
                                }

                                return "📊 Total: {$count} karyawan\n\n" . $result;
                            })
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => $record->scope_type !== 'all')
                    ->collapsible(),

                Infolists\Components\Section::make('Informasi Sistem')
                    ->schema([
                        Infolists\Components\TextEntry::make('createdBy.name')
                            ->label('Dibuat Oleh'),

                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Tanggal Dibuat')
                            ->dateTime('d M Y H:i'),

                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Terakhir Diupdate')
                            ->dateTime('d M Y H:i'),
                    ])
                    ->columns(3),
            ]);
    }
}
