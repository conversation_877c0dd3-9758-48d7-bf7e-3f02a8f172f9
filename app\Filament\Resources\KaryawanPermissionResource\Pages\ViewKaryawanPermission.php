<?php

namespace App\Filament\Resources\KaryawanPermissionResource\Pages;

use App\Filament\Resources\KaryawanPermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewKaryawanPermission extends ViewRecord
{
    protected static string $resource = KaryawanPermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informasi Karyawan')
                    ->schema([
                        Infolists\Components\TextEntry::make('karyawan.nama_lengkap')
                            ->label('Nama Karyawan')
                            ->weight('bold')
                            ->size('lg'),

                        Infolists\Components\TextEntry::make('karyawan.nip')
                            ->label('NIP')
                            ->copyable()
                            ->badge()
                            ->color('gray'),

                        Infolists\Components\TextEntry::make('karyawan_hierarchy')
                            ->label('Hirarki Organisasi')
                            ->getStateUsing(function ($record) {
                                $karyawan = $record->karyawan;
                                if (!$karyawan) return 'Data tidak tersedia';

                                $hierarchy = [];

                                // Entitas
                                if ($karyawan->entitas) {
                                    $hierarchy[] = "🏢 {$karyawan->entitas->nama}";
                                }

                                // Departemen
                                if ($karyawan->departemen) {
                                    $hierarchy[] = "🏛️ {$karyawan->departemen->nama_departemen}";
                                }

                                // Divisi
                                if ($karyawan->divisi) {
                                    $hierarchy[] = "🏗️ {$karyawan->divisi->nama_divisi}";
                                }

                                // Jabatan
                                if ($karyawan->jabatan) {
                                    $hierarchy[] = "👤 {$karyawan->jabatan->nama_jabatan}";
                                }

                                return !empty($hierarchy) ? implode(' → ', $hierarchy) : 'Hirarki tidak lengkap';
                            })
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Informasi Permission')
                    ->schema([
                        Infolists\Components\TextEntry::make('permission_type_label')
                            ->label('Jenis Permission')
                            ->badge()
                            ->color(function ($state) {
                                return match ($state) {
                                    'Approve Cuti/Izin/Sakit' => 'success',
                                    'Lihat Absensi' => 'info',
                                    'Kelola Jadwal' => 'warning',
                                    'Kelola Data Karyawan' => 'danger',
                                    'Lihat Payroll' => 'primary',
                                    default => 'gray',
                                };
                            }),

                        Infolists\Components\TextEntry::make('scope_type_label')
                            ->label('Ruang Lingkup')
                            ->badge()
                            ->color(function ($state) {
                                return match ($state) {
                                    'Semua data dalam sistem' => 'success',
                                    'Entitas tertentu' => 'info',
                                    'Departemen tertentu' => 'warning',
                                    'Divisi tertentu' => 'primary',
                                    'Karyawan tertentu' => 'gray',
                                    default => 'secondary',
                                };
                            }),

                        Infolists\Components\TextEntry::make('scope_description')
                            ->label('Detail Scope')
                            ->columnSpanFull(),

                        Infolists\Components\IconEntry::make('is_active')
                            ->label('Status Permission')
                            ->boolean()
                            ->trueIcon('heroicon-o-check-circle')
                            ->falseIcon('heroicon-o-x-circle')
                            ->trueColor('success')
                            ->falseColor('danger'),

                        Infolists\Components\TextEntry::make('description')
                            ->label('Deskripsi/Catatan')
                            ->placeholder('Tidak ada deskripsi')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Karyawan yang Dibawahi')
                    ->schema([
                        Infolists\Components\TextEntry::make('scope_summary')
                            ->label('Ringkasan Ruang Lingkup')
                            ->getStateUsing(function ($record) {
                                $scopeValues = is_array($record->scope_values) ? $record->scope_values : [];

                                switch ($record->scope_type) {
                                    case 'all':
                                        return '🌐 Dapat mengakses SEMUA karyawan aktif dalam sistem';

                                    case 'entitas':
                                        if (empty($scopeValues)) return '❌ Tidak ada entitas dipilih';
                                        $entitas = \App\Models\Entitas::whereIn('id', $scopeValues)->pluck('nama')->toArray();
                                        return '🏢 Entitas: ' . implode(', ', $entitas);

                                    case 'departemen':
                                        if (empty($scopeValues)) return '❌ Tidak ada departemen dipilih';
                                        $departemenNames = [];
                                        foreach ($scopeValues as $scope) {
                                            if (is_array($scope) && isset($scope['departemen_id'], $scope['entitas_id'])) {
                                                $dept = \App\Models\Departemen::find($scope['departemen_id']);
                                                $entitas = \App\Models\Entitas::find($scope['entitas_id']);
                                                if ($dept && $entitas) {
                                                    $departemenNames[] = "{$dept->nama_departemen} ({$entitas->nama})";
                                                }
                                            }
                                        }
                                        return !empty($departemenNames) ? '🏛️ Departemen: ' . implode(', ', $departemenNames) : '❌ Departemen tidak valid';

                                    case 'divisi':
                                        if (empty($scopeValues)) return '❌ Tidak ada divisi dipilih';
                                        $divisiNames = [];
                                        foreach ($scopeValues as $scope) {
                                            if (is_array($scope) && isset($scope['divisi_id'], $scope['departemen_id'])) {
                                                $divisi = \App\Models\Divisi::find($scope['divisi_id']);
                                                $dept = \App\Models\Departemen::find($scope['departemen_id']);
                                                if ($divisi && $dept) {
                                                    $divisiNames[] = "{$divisi->nama_divisi} ({$dept->nama_departemen})";
                                                }
                                            }
                                        }
                                        return !empty($divisiNames) ? '🏗️ Divisi: ' . implode(', ', $divisiNames) : '❌ Divisi tidak valid';

                                    case 'custom':
                                        if (empty($scopeValues)) return '❌ Tidak ada karyawan dipilih';
                                        $count = count($scopeValues);
                                        return "👥 Karyawan khusus: {$count} orang";

                                    default:
                                        return '❓ Scope tidak dikenal';
                                }
                            })
                            ->badge()
                            ->color('info'),

                        Infolists\Components\TextEntry::make('affected_employees')
                            ->label('Daftar Karyawan yang Dapat Diakses')
                            ->getStateUsing(function ($record) {
                                // Get karyawan yang dapat diakses berdasarkan scope permission ini
                                $karyawanQuery = \App\Models\Karyawan::where('status_aktif', true);
                                $scopeValues = is_array($record->scope_values) ? $record->scope_values : [];

                                switch ($record->scope_type) {
                                    case 'all':
                                        // Semua karyawan aktif
                                        break;

                                    case 'entitas':
                                        if (!empty($scopeValues)) {
                                            $karyawanQuery->whereIn('id_entitas', $scopeValues);
                                        } else {
                                            $karyawanQuery->whereRaw('1 = 0'); // No results
                                        }
                                        break;

                                    case 'departemen':
                                        if (!empty($scopeValues)) {
                                            $karyawanQuery->where(function ($query) use ($scopeValues) {
                                                foreach ($scopeValues as $scope) {
                                                    if (is_array($scope) && isset($scope['departemen_id'], $scope['entitas_id'])) {
                                                        $query->orWhere(function ($subQuery) use ($scope) {
                                                            $subQuery->where('id_departemen', $scope['departemen_id'])
                                                                ->where('id_entitas', $scope['entitas_id']);
                                                        });
                                                    }
                                                }
                                            });
                                        } else {
                                            $karyawanQuery->whereRaw('1 = 0');
                                        }
                                        break;

                                    case 'divisi':
                                        if (!empty($scopeValues)) {
                                            $karyawanQuery->where(function ($query) use ($scopeValues) {
                                                foreach ($scopeValues as $scope) {
                                                    if (is_array($scope) && isset($scope['divisi_id'], $scope['departemen_id'])) {
                                                        $query->orWhere(function ($subQuery) use ($scope) {
                                                            $subQuery->where('id_divisi', $scope['divisi_id'])
                                                                ->where('id_departemen', $scope['departemen_id']);
                                                        });
                                                    }
                                                }
                                            });
                                        } else {
                                            $karyawanQuery->whereRaw('1 = 0');
                                        }
                                        break;

                                    case 'custom':
                                        if (!empty($scopeValues)) {
                                            $karyawanQuery->whereIn('id', $scopeValues);
                                        } else {
                                            $karyawanQuery->whereRaw('1 = 0');
                                        }
                                        break;

                                    default:
                                        $karyawanQuery->whereRaw('1 = 0');
                                        break;
                                }

                                $karyawanList = $karyawanQuery
                                    ->with(['entitas', 'departemen', 'divisi', 'jabatan'])
                                    ->orderBy('nama_lengkap')
                                    ->get();

                                if ($karyawanList->isEmpty()) {
                                    return '❌ Tidak ada karyawan yang dapat diakses dengan scope ini';
                                }

                                $count = $karyawanList->count();

                                if ($record->scope_type === 'all') {
                                    return "✅ Dapat mengakses SEMUA {$count} karyawan aktif dalam sistem";
                                }

                                // Group karyawan by hierarchy
                                $groupedKaryawan = [];
                                foreach ($karyawanList as $k) {
                                    $entitasKey = $k->entitas ? $k->entitas->nama : 'Tanpa Entitas';
                                    $departemenKey = $k->departemen ? $k->departemen->nama_departemen : 'Tanpa Departemen';
                                    $divisiKey = $k->divisi ? $k->divisi->nama_divisi : 'Tanpa Divisi';

                                    $groupedKaryawan[$entitasKey][$departemenKey][$divisiKey][] = $k;
                                }

                                $result = "<div style='font-family: monospace;'>";
                                $result .= "<div style='background: #f3f4f6; padding: 12px; border-radius: 8px; margin-bottom: 16px; border-left: 4px solid #3b82f6;'>";
                                $result .= "<strong>📊 Total: {$count} karyawan</strong>";
                                $result .= "</div>";

                                foreach ($groupedKaryawan as $entitasName => $departemenGroup) {
                                    $result .= "<div style='background: #fef3c7; padding: 12px; border-radius: 8px; margin-bottom: 12px; border-left: 4px solid #f59e0b;'>";
                                    $result .= "<strong>🏢 {$entitasName}</strong>";

                                    foreach ($departemenGroup as $departemenName => $divisiGroup) {
                                        $result .= "<div style='background: #dbeafe; padding: 10px; border-radius: 6px; margin: 8px 0; border-left: 3px solid #3b82f6;'>";
                                        $result .= "<strong>🏛️ {$departemenName}</strong>";

                                        foreach ($divisiGroup as $divisiName => $karyawanInDivisi) {
                                            $result .= "<div style='background: #dcfce7; padding: 8px; border-radius: 4px; margin: 6px 0; border-left: 2px solid #22c55e;'>";
                                            $result .= "<strong>🏗️ {$divisiName}</strong> <span style='color: #6b7280;'>(" . count($karyawanInDivisi) . " orang)</span>";

                                            $result .= "<div style='margin-top: 6px;'>";
                                            foreach ($karyawanInDivisi as $k) {
                                                $jabatan = $k->jabatan ? $k->jabatan->nama_jabatan : 'Tanpa Jabatan';
                                                $nip = $k->nip ? "<span style='color: #6b7280;'>({$k->nip})</span>" : '';

                                                $result .= "<div style='background: white; padding: 6px 8px; margin: 2px 0; border-radius: 4px; border: 1px solid #e5e7eb;'>";
                                                $result .= "👤 <strong>{$k->nama_lengkap}</strong> {$nip}";
                                                $result .= "<br><span style='color: #6b7280; font-size: 0.9em; margin-left: 20px;'>📋 {$jabatan}</span>";
                                                $result .= "</div>";
                                            }
                                            $result .= "</div>";

                                            $result .= "</div>";
                                        }
                                        $result .= "</div>";
                                    }
                                    $result .= "</div>";
                                }

                                $result .= "</div>";

                                return $result;
                            })
                            ->markdown()
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Infolists\Components\Section::make('Informasi Sistem')
                    ->schema([
                        Infolists\Components\TextEntry::make('createdBy.name')
                            ->label('Dibuat Oleh'),

                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Tanggal Dibuat')
                            ->dateTime('d M Y H:i'),

                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Terakhir Diupdate')
                            ->dateTime('d M Y H:i'),
                    ])
                    ->columns(3),
            ]);
    }
}
