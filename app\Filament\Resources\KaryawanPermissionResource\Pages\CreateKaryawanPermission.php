<?php

namespace App\Filament\Resources\KaryawanPermissionResource\Pages;

use App\Filament\Resources\KaryawanPermissionResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateKaryawanPermission extends CreateRecord
{
    protected static string $resource = KaryawanPermissionResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = Auth::id();

        // Handle scope values based on scope type
        switch ($data['scope_type']) {
            case 'entitas':
                $data['scope_values'] = $data['scope_values_entitas'] ?? null;
                break;
            case 'departemen':
                $data['scope_values'] = $data['scope_values_departemen'] ?? null;
                break;
            case 'divisi':
                $data['scope_values'] = $data['scope_values_divisi'] ?? null;
                break;
            case 'custom':
                $data['scope_values'] = $data['scope_values_custom'] ?? null;
                break;
            case 'all':
            default:
                $data['scope_values'] = null;
                break;
        }

        // Remove temporary fields
        unset($data['scope_values_entitas']);
        unset($data['scope_values_departemen']);
        unset($data['scope_values_divisi']);
        unset($data['scope_values_custom']);

        // Remove individual department divisi fields
        $departemen = \App\Models\Departemen::all();
        foreach ($departemen as $dept) {
            unset($data["divisi_dept_{$dept->id}"]);
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
