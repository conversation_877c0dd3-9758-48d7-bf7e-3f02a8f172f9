<?php

namespace App\Filament\Resources\KaryawanPermissionResource\Pages;

use App\Filament\Resources\KaryawanPermissionResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateKaryawanPermission extends CreateRecord
{
    protected static string $resource = KaryawanPermissionResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = Auth::id();

        // Handle scope values based on scope type
        switch ($data['scope_type']) {
            case 'entitas':
                $data['scope_values'] = $data['scope_values_entitas'] ?? null;
                break;

            case 'departemen':
                // Combine departemen from all entitas with entitas context
                $departemenWithEntitas = [];
                $entitas = \App\Models\Entitas::all();
                foreach ($entitas as $ent) {
                    $entDepartemen = $data["departemen_entitas_{$ent->id}"] ?? [];
                    if (is_array($entDepartemen) && !empty($entDepartemen)) {
                        foreach ($entDepartemen as $deptId) {
                            $departemenWithEntitas[] = [
                                'departemen_id' => $deptId,
                                'entitas_id' => $ent->id
                            ];
                        }
                    }
                }
                $data['scope_values'] = !empty($departemenWithEntitas) ? $departemenWithEntitas : null;
                break;

            case 'divisi':
                // Combine divisi from all entitas with entitas context
                $divisiWithEntitas = [];
                $entitas = \App\Models\Entitas::all();
                foreach ($entitas as $ent) {
                    $entDivisi = $data["divisi_entitas_{$ent->id}"] ?? [];
                    if (is_array($entDivisi) && !empty($entDivisi)) {
                        foreach ($entDivisi as $divisiId) {
                            $divisiWithEntitas[] = [
                                'divisi_id' => $divisiId,
                                'entitas_id' => $ent->id
                            ];
                        }
                    }
                }
                $data['scope_values'] = !empty($divisiWithEntitas) ? $divisiWithEntitas : null;
                break;

            case 'custom':
                $data['scope_values'] = $data['scope_values_custom'] ?? null;
                break;

            case 'all':
            default:
                $data['scope_values'] = null;
                break;
        }

        // Remove temporary fields
        unset($data['scope_values_entitas']);
        unset($data['scope_values_departemen']);
        unset($data['scope_values_divisi']);
        unset($data['scope_values_custom']);

        // Remove individual entitas departemen and divisi fields
        $entitas = \App\Models\Entitas::all();
        foreach ($entitas as $ent) {
            unset($data["departemen_entitas_{$ent->id}"]);
            unset($data["divisi_entitas_{$ent->id}"]);
        }

        // Remove individual department divisi fields (legacy cleanup)
        $departemen = \App\Models\Departemen::all();
        foreach ($departemen as $dept) {
            unset($data["divisi_dept_{$dept->id}"]);
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
