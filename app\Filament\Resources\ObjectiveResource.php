<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ObjectiveResource\Pages;
use App\Filament\Resources\ObjectiveResource\RelationManagers;
use App\Models\Objective;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

use Filament\Support\Enums\FontWeight;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Facades\Auth;

class ObjectiveResource extends Resource
{
    protected static ?string $model = Objective::class;

    protected static ?string $navigationIcon = 'heroicon-o-flag';

    protected static ?string $navigationGroup = 'OKR Management';

    protected static ?string $navigationLabel = 'Objectives';

    protected static ?string $modelLabel = 'Objective';

    protected static ?string $pluralModelLabel = 'Objectives';

    protected static ?int $navigationSort = 1;

    // Access control
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return in_array($user->role, ['admin', 'supervisor']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Objective')
                    ->schema([
                        Forms\Components\TextInput::make('nama_objective')
                            ->label('Nama Objective')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\RichEditor::make('deskripsi')
                            ->label('Deskripsi')
                            ->columnSpanFull(),

                        Forms\Components\Select::make('okr_period_id')
                            ->label('OKR Period')
                            ->relationship('okrPeriod', 'nama_periode')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->getOptionLabelFromRecordUsing(fn($record) => $record->nama_periode . ' (' . $record->tanggal_mulai->format('d M Y') . ' - ' . $record->tanggal_selesai->format('d M Y') . ')')
                            ->default(function () {
                                $activePeriod = \App\Models\OkrPeriod::where('is_active', true)->first();
                                return $activePeriod?->id;
                            })
                            ->live()
                            ->afterStateUpdated(function (callable $set, $state) {
                                if ($state) {
                                    $period = \App\Models\OkrPeriod::find($state);
                                    if ($period) {
                                        $set('periode_mulai', $period->tanggal_mulai->format('Y-m-d'));
                                        $set('periode_selesai', $period->tanggal_selesai->format('Y-m-d'));
                                    }
                                }
                            })
                            ->helperText('Pilih periode OKR yang sesuai untuk objective ini')
                            ->columnSpanFull(),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('periode_mulai')
                                    ->label('Periode Mulai')
                                    ->required()
                                    ->readOnly()
                                    ->dehydrated()
                                    ->default(function () {
                                        $activePeriod = \App\Models\OkrPeriod::where('is_active', true)->first();
                                        return $activePeriod?->tanggal_mulai?->format('Y-m-d');
                                    })
                                    ->helperText('Otomatis diisi berdasarkan OKR Period yang dipilih'),

                                Forms\Components\DatePicker::make('periode_selesai')
                                    ->label('Periode Selesai')
                                    ->required()
                                    ->readOnly()
                                    ->dehydrated()
                                    ->default(function () {
                                        $activePeriod = \App\Models\OkrPeriod::where('is_active', true)->first();
                                        return $activePeriod?->tanggal_selesai?->format('Y-m-d');
                                    })
                                    ->helperText('Otomatis diisi berdasarkan OKR Period yang dipilih'),
                            ]),

                        Forms\Components\DatePicker::make('target_completion')
                            ->label('Target Penyelesaian')
                            ->after('periode_mulai')
                            ->before('periode_selesai'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Assignment & Status')
                    ->schema([
                        Forms\Components\Select::make('owner_id')
                            ->label('Pemilik Objective')
                            ->relationship('owner', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('departemen_id')
                            ->label('Departemen')
                            ->options(\App\Models\Departemen::pluck('nama_departemen', 'id'))
                            ->searchable()
                            ->preload()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set) {
                                $set('divisi_id', null);
                            })
                            ->dehydrated(),

                        Forms\Components\Select::make('divisi_id')
                            ->label('Divisi')
                            ->options(function (callable $get) {
                                $departemenId = $get('departemen_id');
                                if (!$departemenId) {
                                    return [];
                                }
                                return \App\Models\Divisi::where('departemen_id', $departemenId)
                                    ->pluck('nama_divisi', 'id');
                            })
                            ->searchable()
                            ->disabled(fn(callable $get) => !$get('departemen_id'))
                            ->placeholder(fn(callable $get) => $get('departemen_id') ? 'Pilih divisi...' : 'Pilih departemen terlebih dahulu')
                            ->helperText('Divisi akan muncul setelah memilih departemen')
                            ->dehydrated(),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'draft' => 'Draft',
                                'active' => 'Aktif',
                                'completed' => 'Selesai',
                                'cancelled' => 'Dibatalkan',
                            ])
                            ->required()
                            ->default('draft')
                            ->native(false),

                        Forms\Components\TextInput::make('progress_percentage')
                            ->label('Progress (%)')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->default(0)
                            ->suffix('%')
                            ->disabled(fn(string $operation): bool => $operation === 'create'),
                    ])
                    ->columns(2),

                Forms\Components\Hidden::make('created_by')
                    ->default(Auth::id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nama_objective')
                    ->label('Nama Objective')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Medium)
                    ->wrap(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'draft' => 'gray',
                        'active' => 'success',
                        'completed' => 'primary',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'draft' => 'Draft',
                        'active' => 'Aktif',
                        'completed' => 'Selesai',
                        'cancelled' => 'Dibatalkan',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('progress_percentage')
                    ->label('Progress')
                    ->formatStateUsing(fn($state) => $state . '%')
                    ->color(fn($state) => match (true) {
                        $state >= 80 => 'success',
                        $state >= 60 => 'warning',
                        $state >= 40 => 'info',
                        default => 'danger',
                    })
                    ->badge(),

                Tables\Columns\TextColumn::make('owner.name')
                    ->label('Pemilik')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('departemen.nama_departemen')
                    ->label('Departemen')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('divisi.nama_divisi')
                    ->label('Divisi')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('periode_mulai')
                    ->label('Periode Mulai')
                    ->date('d M Y')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('periode_selesai')
                    ->label('Periode Selesai')
                    ->date('d M Y')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('target_completion')
                    ->label('Target Selesai')
                    ->date('d M Y')
                    ->sortable()
                    ->toggleable()
                    ->color(fn($record) => $record->is_overdue ? 'danger' : 'primary'),

                Tables\Columns\TextColumn::make('key_results_count')
                    ->label('Key Results')
                    ->counts('keyResults')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('tactics_count')
                    ->label('Tactics')
                    ->counts('tactics')
                    ->badge()
                    ->color('warning'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'draft' => 'Draft',
                        'active' => 'Aktif',
                        'completed' => 'Selesai',
                        'cancelled' => 'Dibatalkan',
                    ])
                    ->multiple(),

                SelectFilter::make('owner_id')
                    ->label('Pemilik')
                    ->relationship('owner', 'name')
                    ->searchable()
                    ->preload()
                    ->multiple(),

                SelectFilter::make('departemen_id')
                    ->label('Departemen')
                    ->relationship('departemen', 'nama_departemen')
                    ->searchable()
                    ->preload()
                    ->multiple(),

                Tables\Filters\Filter::make('progress_range')
                    ->label('Progress Range')
                    ->form([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('progress_from')
                                    ->label('From (%)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(100),
                                Forms\Components\TextInput::make('progress_to')
                                    ->label('To (%)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(100),
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['progress_from'],
                                fn(Builder $query, $value): Builder => $query->where('progress_percentage', '>=', $value),
                            )
                            ->when(
                                $data['progress_to'],
                                fn(Builder $query, $value): Builder => $query->where('progress_percentage', '<=', $value),
                            );
                    }),

                Tables\Filters\Filter::make('date_range')
                    ->label('Period Range')
                    ->form([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('periode_from')
                                    ->label('From Date'),
                                Forms\Components\DatePicker::make('periode_to')
                                    ->label('To Date'),
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['periode_from'],
                                fn(Builder $query, $date): Builder => $query->where('periode_mulai', '>=', $date),
                            )
                            ->when(
                                $data['periode_to'],
                                fn(Builder $query, $date): Builder => $query->where('periode_selesai', '<=', $date),
                            );
                    }),

                Tables\Filters\TernaryFilter::make('is_overdue')
                    ->label('Overdue Status')
                    ->placeholder('All objectives')
                    ->trueLabel('Overdue only')
                    ->falseLabel('Not overdue')
                    ->queries(
                        true: fn(Builder $query) => $query->where('target_completion', '<', now())->where('status', '!=', 'completed'),
                        false: fn(Builder $query) => $query->where(function ($q) {
                            $q->where('target_completion', '>=', now())
                                ->orWhere('status', 'completed')
                                ->orWhereNull('target_completion');
                        }),
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Lihat'),
                Tables\Actions\EditAction::make()
                    ->label('Edit'),
                Tables\Actions\DeleteAction::make()
                    ->label('Hapus'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Hapus Terpilih'),
                ]),
            ])
            ->defaultSort('objectives.created_at', 'desc')
            ->striped()
            ->paginated([10, 25, 50]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\KeyResultsRelationManager::class,
            RelationManagers\TacticsRelationManager::class,
            RelationManagers\TasksRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListObjectives::route('/'),
            'create' => Pages\CreateObjective::route('/create'),
            // 'view' => Pages\ViewObjective::route('/{record}'),
            'edit' => Pages\EditObjective::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withCount(['keyResults', 'tactics'])
            ->with(['owner:id,name', 'departemen:id,nama_departemen', 'divisi:id,nama_divisi']);
    }
}
