<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Outlet;

class OutletSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $outlets = [
            [
                'name' => 'Viera FnB Jakarta Pusat',
                'category' => 'FnB',
                'address' => 'Jl. Sudirman No. 123, Jakarta Pusat',
                'description' => 'Outlet FnB utama di Jakarta Pusat dengan menu lengkap',
                'is_active' => true,
            ],
            [
                'name' => 'Viera FnB Bandung',
                'category' => 'FnB',
                'address' => 'Jl. Asia Afrika No. 45, Bandung',
                'description' => 'Outlet FnB cabang di Bandung dengan menu khas Sunda',
                'is_active' => true,
            ],
            [
                'name' => 'Viera Oleh-oleh Surabaya',
                'category' => 'VOO',
                'address' => 'Jl. Pemuda No. 67, Surabaya',
                'description' => 'Outlet oleh-oleh di Surabaya dengan produk khas Jawa Timur',
                'is_active' => true,
            ],
            [
                'name' => 'Viera Oleh-oleh Medan',
                'category' => 'VOO',
                'address' => 'Jl. Gatot Subroto No. 89, Medan',
                'description' => 'Outlet oleh-oleh di Medan dengan produk khas Sumatera',
                'is_active' => true,
            ],
            [
                'name' => 'Viera FnB Yogyakarta',
                'category' => 'FnB',
                'address' => 'Jl. Malioboro No. 100, Yogyakarta',
                'description' => 'Outlet FnB di Yogyakarta dengan menu tradisional',
                'is_active' => true,
            ],
            [
                'name' => 'Viera Oleh-oleh Bali',
                'category' => 'VOO',
                'address' => 'Jl. Sunset Road No. 25, Bali',
                'description' => 'Outlet oleh-oleh di Bali dengan produk khas Bali',
                'is_active' => true,
            ],
        ];

        foreach ($outlets as $outlet) {
            Outlet::create($outlet);
        }
    }
}
