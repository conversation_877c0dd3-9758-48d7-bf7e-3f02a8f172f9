<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Filter Form -->
        <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('heading', null, []); ?> 
                Filter Report
             <?php $__env->endSlot(); ?>

            <div class="space-y-4">
                <?php echo e($this->form); ?>


                <div class="mt-4">
                    <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['wire:click' => 'generateReport','color' => 'primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'generateReport','color' => 'primary']); ?>
                        Generate Report
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
                </div>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

        <!-- Report Results -->
        <!--[if BLOCK]><![endif]--><?php if(!empty($reportData)): ?>
            <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                 <?php $__env->slot('heading', null, []); ?> 
                    <?php echo e($reportData['outlet']->name); ?> - <?php echo e($reportData['period']); ?>

                 <?php $__env->endSlot(); ?>

                <!--[if BLOCK]><![endif]--><?php if(isset($reportData['has_data']) && !$reportData['has_data']): ?>
                    <!-- No Data Message -->
                    <div class="text-center py-12">
                        <div class="mx-auto h-12 w-12 text-gray-400">
                            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
                        <p class="mt-1 text-sm text-gray-500">
                            No transaction data available for <?php echo e($reportData['outlet']->name); ?> in
                            <?php echo e($reportData['period']); ?>.
                        </p>
                        <div class="mt-6">
                            <a href="<?php echo e(route('filament.akunting.resources.daily-transactions.create')); ?>"
                                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                Add Transaction
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Detailed P&L Report -->

                      <!-- Category Breakdown -->
                    <!-- Summary Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <!-- Revenue Card -->
                        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                            </path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-green-900">Total Revenue</h3>
                                    <p class="text-2xl font-bold text-green-600">
                                        <?php echo e($this->formatCurrency($reportData['summary']['total_revenue'])); ?>

                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Expense Card -->
                        <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M20 12H4"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-red-900">Total Expense</h3>
                                    <p class="text-2xl font-bold text-red-600">
                                        <?php echo e($this->formatCurrency($reportData['summary']['total_expense'])); ?>

                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Net Profit Card -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-blue-900">Net Profit</h3>
                                    <p
                                        class="text-2xl font-bold <?php echo e($reportData['summary']['laba_bersih'] >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                                        <?php echo e($this->formatCurrency($reportData['summary']['laba_bersih'])); ?>

                                    </p>
                                    <p class="text-sm text-gray-600">
                                        <?php echo e($this->getPercentageDisplay($reportData['summary']['laba_bersih'], $reportData['summary']['total_revenue'])); ?>

                                        of revenue
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">
                                            KETERANGAN
                                        </th>
                                        <th
                                            class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">
                                            TOTAL
                                        </th>
                                        <th
                                            class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                                            RASIO
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- I. PENDAPATAN -->
                                    <tr class="bg-blue-50">
                                        <td class="px-6 py-3 text-sm font-bold text-gray-900">I. PENDAPATAN</td>
                                        <td class="px-6 py-3"></td>
                                        <td class="px-6 py-3"></td>
                                    </tr>
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['cash'] > 0): ?>
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Cash</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                <?php echo e(number_format($reportData['revenue_breakdown']['cash'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['debit'] > 0): ?>
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Debit</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                <?php echo e(number_format($reportData['revenue_breakdown']['debit'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['transfer'] > 0): ?>
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Transfer</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                <?php echo e(number_format($reportData['revenue_breakdown']['transfer'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['qris'] > 0): ?>
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan QRIS</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                <?php echo e(number_format($reportData['revenue_breakdown']['qris'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['gojek'] > 0): ?>
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Gojek</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                <?php echo e(number_format($reportData['revenue_breakdown']['gojek'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['grab_ovo'] > 0): ?>
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Grab Ovo</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                <?php echo e(number_format($reportData['revenue_breakdown']['grab_ovo'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['revenue_breakdown']['sewa_rak'] > 0): ?>
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Pendapatan Sewa Rak</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                <?php echo e(number_format($reportData['revenue_breakdown']['sewa_rak'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <tr class="bg-green-50 font-semibold">
                                        <td class="px-6 py-3 text-sm text-gray-900 pl-12">Total Pendapatan</td>
                                        <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                            <?php echo e(number_format($reportData['summary']['total_revenue'], 0, ',', '.')); ?>

                                        </td>
                                        <td class="px-6 py-3"></td>
                                    </tr>

                                    <!-- II. BEBAN PENJUALAN -->
                                    <tr class="bg-red-50">
                                        <td class="px-6 py-3 text-sm font-bold text-gray-900">II. BEBAN PENJUALAN</td>
                                        <td class="px-6 py-3"></td>
                                        <td class="px-6 py-3"></td>
                                    </tr>
                                    <?php if($reportData['expense_breakdown']['beban_bahan_baku']['total'] > 0): ?>
                                        <?php $__currentLoopData = $reportData['expense_breakdown']['beban_bahan_baku']['details']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $amount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <!--[if BLOCK]><![endif]--><?php if($amount > 0): ?>
                                                <tr>
                                                    <td class="px-6 py-2 text-sm text-gray-700 pl-12">
                                                        <!--[if BLOCK]><![endif]--><?php switch($key):
                                                            case ('tagihan_rkv'): ?>
                                                                Tagihan RKV
                                                            <?php break; ?>

                                                            <?php case ('tagihan_mitra'): ?>
                                                                Tagihan Mitra
                                                            <?php break; ?>

                                                            <?php case ('tagihan_supplier'): ?>
                                                                Tagihan Supplier
                                                            <?php break; ?>

                                                            <?php case ('bahan_baku_lainnya'): ?>
                                                                Bahan Baku Lainnya
                                                            <?php break; ?>
                                                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </td>
                                                    <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                        <?php echo e(number_format($amount, 0, ',', '.')); ?></td>
                                                    <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                        <?php echo e($this->getPercentageDisplay($amount, $reportData['summary']['total_revenue'])); ?>

                                                    </td>
                                                </tr>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        <tr class="bg-red-100 font-semibold">
                                            <td class="px-6 py-3 text-sm text-gray-900 pl-12">Total Beban Bahan Baku
                                            </td>
                                            <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                                <?php echo e(number_format($reportData['expense_breakdown']['beban_bahan_baku']['total'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-3 text-sm text-gray-600 text-right font-semibold">
                                                <?php echo e($this->getPercentageDisplay($reportData['expense_breakdown']['beban_bahan_baku']['total'], $reportData['summary']['total_revenue'])); ?>

                                            </td>
                                        </tr>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <tr class="bg-green-100 font-bold">
                                        <td class="px-6 py-3 text-sm text-gray-900">Laba Kotor</td>
                                        <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                            <?php echo e(number_format($reportData['summary']['laba_kotor'], 0, ',', '.')); ?></td>
                                        <td class="px-6 py-3 text-sm text-gray-600 text-right font-bold">
                                            <?php echo e($this->getPercentageDisplay($reportData['summary']['laba_kotor'], $reportData['summary']['total_revenue'])); ?>

                                        </td>
                                    </tr>

                                    <!-- III. BEBAN OPERASIONAL -->
                                    <tr class="bg-orange-50">
                                        <td class="px-6 py-3 text-sm font-bold text-gray-900">III. BEBAN OPERASIONAL
                                        </td>
                                        <td class="px-6 py-3"></td>
                                        <td class="px-6 py-3"></td>
                                    </tr>

                                    <!-- Beban GA -->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['expense_breakdown']['beban_ga']['total'] > 0): ?>
                                        <tr class="bg-blue-25">
                                            <td class="px-6 py-2 text-sm font-semibold text-gray-800 pl-8">Total Biaya
                                                Belanja GA</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right font-semibold">Rp
                                                <?php echo e(number_format($reportData['expense_breakdown']['beban_ga']['total'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                <?php echo e($this->getPercentageDisplay($reportData['expense_breakdown']['beban_ga']['total'], $reportData['summary']['total_revenue'])); ?>

                                            </td>
                                        </tr>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $reportData['expense_breakdown']['beban_ga']['details']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $amount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <!--[if BLOCK]><![endif]--><?php if($amount > 0): ?>
                                                <tr>
                                                    <td class="px-6 py-1 text-sm text-gray-600 pl-16">
                                                        <!--[if BLOCK]><![endif]--><?php switch($key):
                                                            case ('material_bangunan'): ?>
                                                                Material Bangunan/Kabel/Bayar Tukang
                                                            <?php break; ?>

                                                            <?php case ('service_oven_freezer_packing'): ?>
                                                                Service Oven dan Freezer/Mobil/AC/Mesin Packing/Genset
                                                            <?php break; ?>

                                                            <?php case ('service_equipment'): ?>
                                                                Service Freezer/Mobil/AC/Motor/CCTV/dll
                                                            <?php break; ?>

                                                            <?php case ('belanja_ga'): ?>
                                                                Belanja GA
                                                            <?php break; ?>

                                                            <?php case ('cuci_mobil_oli'): ?>
                                                                Cuci Mobil/Isi Angin/Tambal Ban/Ganti Oli
                                                            <?php break; ?>

                                                            <?php case ('kertas_thermal'): ?>
                                                                Kertas Thermal/Kertas Label
                                                            <?php break; ?>

                                                            <?php case ('keperluan_genset'): ?>
                                                                Keperluan Genset/Dexlite Genset
                                                            <?php break; ?>

                                                            <?php case ('bensin_luxio_putih'): ?>
                                                                Bensin Luxio Putih
                                                            <?php break; ?>

                                                            <?php case ('bensin_luxio_silver'): ?>
                                                                Bensin Luxio Silver
                                                            <?php break; ?>

                                                            <?php default: ?>
                                                                <?php echo e(ucwords(str_replace('_', ' ', $key))); ?>

                                                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </td>
                                                    <td class="px-6 py-1 text-sm text-gray-700 text-right">Rp
                                                        <?php echo e(number_format($amount, 0, ',', '.')); ?></td>
                                                    <td class="px-6 py-1"></td>
                                                </tr>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!-- Beban Promosi -->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['expense_breakdown']['beban_promosi']['total'] > 0): ?>
                                        <tr class="bg-purple-25">
                                            <td class="px-6 py-2 text-sm font-semibold text-gray-800 pl-8">Total Beban
                                                Promosi</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right font-semibold">Rp
                                                <?php echo e(number_format($reportData['expense_breakdown']['beban_promosi']['total'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                <?php echo e($this->getPercentageDisplay($reportData['expense_breakdown']['beban_promosi']['total'], $reportData['summary']['total_revenue'])); ?>

                                            </td>
                                        </tr>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $reportData['expense_breakdown']['beban_promosi']['details']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $amount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <!--[if BLOCK]><![endif]--><?php if($amount > 0): ?>
                                                <tr>
                                                    <td class="px-6 py-1 text-sm text-gray-600 pl-16">
                                                        <!--[if BLOCK]><![endif]--><?php switch($key):
                                                            case ('free_talam_rs'): ?>
                                                                Free talam RS. annisa
                                                            <?php break; ?>

                                                            <?php case ('free_gift_ultah'): ?>
                                                                Free gift ultah
                                                            <?php break; ?>

                                                            <?php case ('kue_marketing'): ?>
                                                                kue keperluan marketing/Pengeluaran Marketing
                                                            <?php break; ?>

                                                            <?php case ('tester'): ?>
                                                                Tester
                                                            <?php break; ?>

                                                            <?php case ('free_bundling_kuker'): ?>
                                                                Free Bundling Kuker
                                                            <?php break; ?>

                                                            <?php case ('gift_card'): ?>
                                                                Gift Card
                                                            <?php break; ?>

                                                            <?php default: ?>
                                                                <?php echo e(ucwords(str_replace('_', ' ', $key))); ?>

                                                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </td>
                                                    <td class="px-6 py-1 text-sm text-gray-700 text-right">Rp
                                                        <?php echo e(number_format($amount, 0, ',', '.')); ?></td>
                                                    <td class="px-6 py-1"></td>
                                                </tr>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!-- Beban Utilitas -->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['expense_breakdown']['beban_utilitas']['total'] > 0): ?>
                                        <tr class="bg-yellow-25">
                                            <td class="px-6 py-2 text-sm font-semibold text-gray-800 pl-8">Total Beban
                                                Utilitas</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right font-semibold">Rp
                                                <?php echo e(number_format($reportData['expense_breakdown']['beban_utilitas']['total'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                <?php echo e($this->getPercentageDisplay($reportData['expense_breakdown']['beban_utilitas']['total'], $reportData['summary']['total_revenue'])); ?>

                                            </td>
                                        </tr>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $reportData['expense_breakdown']['beban_utilitas']['details']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $amount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <!--[if BLOCK]><![endif]--><?php if($amount > 0): ?>
                                                <tr>
                                                    <td class="px-6 py-1 text-sm text-gray-600 pl-16">
                                                        <!--[if BLOCK]><![endif]--><?php switch($key):
                                                            case ('listrik'): ?>
                                                                Bayar Listrik
                                                            <?php break; ?>

                                                            <?php case ('internet_pulsa'): ?>
                                                                Bayar Indihome/Pulsa/Paket Telepon
                                                            <?php break; ?>

                                                            <?php case ('pest_control'): ?>
                                                                Jasa Pengendalian Hama (Petsco)
                                                            <?php break; ?>

                                                            <?php case ('kebersihan'): ?>
                                                                Uang Kebersihan (Angkut Sampah)/Uang Ronda/PDAM
                                                            <?php break; ?>

                                                            <?php default: ?>
                                                                <?php echo e(ucwords(str_replace('_', ' ', $key))); ?>

                                                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </td>
                                                    <td class="px-6 py-1 text-sm text-gray-700 text-right">Rp
                                                        <?php echo e(number_format($amount, 0, ',', '.')); ?></td>
                                                    <td class="px-6 py-1"></td>
                                                </tr>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!-- Other Operational Expenses -->
                                    <!-- Pajak -->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['expense_breakdown']['pajak']['total'] > 0): ?>
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Bayar PPh/PPN/PBB</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                <?php echo e(number_format($reportData['expense_breakdown']['pajak']['total'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2"></td>
                                        </tr>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!-- BPJS -->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['expense_breakdown']['bpjs']['total'] > 0): ?>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $reportData['expense_breakdown']['bpjs']['details']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $amount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <!--[if BLOCK]><![endif]--><?php if($amount > 0): ?>
                                                <tr>
                                                    <td class="px-6 py-2 text-sm text-gray-700 pl-12">
                                                        <!--[if BLOCK]><![endif]--><?php switch($key):
                                                            case ('bpjs_kesehatan'): ?>
                                                                BPJS Kesehatan
                                                            <?php break; ?>

                                                            <?php case ('bpjs_tk'): ?>
                                                                BPJS TK
                                                            <?php break; ?>

                                                            <?php default: ?>
                                                                <?php echo e(ucwords(str_replace('_', ' ', $key))); ?>

                                                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </td>
                                                    <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                        <?php echo e(number_format($amount, 0, ',', '.')); ?></td>
                                                    <td class="px-6 py-2"></td>
                                                </tr>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!-- Ongkir -->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['expense_breakdown']['ongkir']['total'] > 0): ?>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $reportData['expense_breakdown']['ongkir']['details']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $amount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <!--[if BLOCK]><![endif]--><?php if($amount > 0): ?>
                                                <tr>
                                                    <td class="px-6 py-2 text-sm text-gray-700 pl-12">
                                                        <!--[if BLOCK]><![endif]--><?php switch($key):
                                                            case ('ongkir_customer_refund'): ?>
                                                                Ongkir Customer/Refund
                                                            <?php break; ?>

                                                            <?php case ('fee_supir_bus'): ?>
                                                                Fee supir bus
                                                            <?php break; ?>

                                                            <?php case ('ongkir_cabang'): ?>
                                                                Ongkir ke cabang
                                                            <?php break; ?>

                                                            <?php default: ?>
                                                                <?php echo e(ucwords(str_replace('_', ' ', $key))); ?>

                                                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </td>
                                                    <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                        <?php echo e(number_format($amount, 0, ',', '.')); ?></td>
                                                    <td class="px-6 py-2"></td>
                                                </tr>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!-- Other -->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['expense_breakdown']['other']['total'] > 0): ?>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $reportData['expense_breakdown']['other']['details']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $amount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <!--[if BLOCK]><![endif]--><?php if($amount > 0): ?>
                                                <tr>
                                                    <td class="px-6 py-2 text-sm text-gray-700 pl-12">
                                                        <!--[if BLOCK]><![endif]--><?php switch($key):
                                                            case ('pengeluaran_point'): ?>
                                                                Pengeluaran Point
                                                            <?php break; ?>

                                                            <?php default: ?>
                                                                <?php echo e(ucwords(str_replace('_', ' ', $key))); ?>

                                                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </td>
                                                    <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                        <?php echo e(number_format($amount, 0, ',', '.')); ?></td>
                                                    <td class="px-6 py-2"></td>
                                                </tr>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!-- Komisi Bank -->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['expense_breakdown']['komisi_bank']['total'] > 0): ?>
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Komisi Bank dan Gojek
                                                (debit,qr,gojek,grab)</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                <?php echo e(number_format($reportData['expense_breakdown']['komisi_bank']['total'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                <?php echo e($this->getPercentageDisplay($reportData['expense_breakdown']['komisi_bank']['total'], $reportData['summary']['total_revenue'])); ?>

                                            </td>
                                        </tr>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!-- Gaji -->
                                    <!--[if BLOCK]><![endif]--><?php if($reportData['expense_breakdown']['gaji']['total'] > 0): ?>
                                        <tr>
                                            <td class="px-6 py-2 text-sm text-gray-700 pl-12">Gaji karyawan</td>
                                            <td class="px-6 py-2 text-sm text-gray-900 text-right">Rp
                                                <?php echo e(number_format($reportData['expense_breakdown']['gaji']['total'], 0, ',', '.')); ?>

                                            </td>
                                            <td class="px-6 py-2 text-sm text-gray-600 text-right">
                                                <?php echo e($this->getPercentageDisplay($reportData['expense_breakdown']['gaji']['total'], $reportData['summary']['total_revenue'])); ?>

                                            </td>
                                        </tr>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!-- Total Pengeluaran Operasional -->
                                    <tr class="bg-orange-100 font-semibold">
                                        <td class="px-6 py-3 text-sm text-gray-900 pl-8">TOTAL BEBAN OPERASIONAL</td>
                                        <td class="px-6 py-3 text-sm text-gray-900 text-right">Rp
                                            <?php echo e(number_format($reportData['summary']['total_beban_operasional'], 0, ',', '.')); ?>

                                        </td>
                                        <td class="px-6 py-3 text-sm text-gray-600 text-right font-semibold">
                                            <?php echo e($this->getPercentageDisplay($reportData['summary']['total_beban_operasional'], $reportData['summary']['total_revenue'])); ?>

                                        </td>
                                    </tr>

                                    <!-- LABA BERSIH -->
                                    <tr class="bg-green-200 font-bold text-lg">
                                        <td class="px-6 py-4 text-gray-900">LABA BERSIH</td>
                                        <td class="px-6 py-4 text-gray-900 text-right">Rp
                                            <?php echo e(number_format($reportData['summary']['laba_bersih'], 0, ',', '.')); ?>

                                        </td>
                                        <td class="px-6 py-4 text-gray-600 text-right font-bold">
                                            <?php echo e($this->getPercentageDisplay($reportData['summary']['laba_bersih'], $reportData['summary']['total_revenue'])); ?>

                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>




                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/akunting/pages/monthly-outlet-report.blade.php ENDPATH**/ ?>