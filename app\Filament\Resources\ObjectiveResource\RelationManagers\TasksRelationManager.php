<?php

namespace App\Filament\Resources\ObjectiveResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Auth;
use App\Models\Task;

class TasksRelationManager extends RelationManager
{
    protected static string $relationship = 'tasks';

    protected static ?string $title = 'Tasks';
    protected static ?string $modelLabel = 'Task';
    protected static ?string $pluralModelLabel = 'Tasks';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Detail Task')
                    ->description('Informasi dasar task yang terhubung dengan objective')
                    ->schema([
                        Forms\Components\Select::make('project_id')
                            ->label('Kegiatan')
                            ->relationship('project', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\TextInput::make('name')
                            ->label('Nama Task')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Assignment & Timeline')
                    ->description('Penugasan dan jadwal task')
                    ->schema([
                        Forms\Components\Select::make('assigned_to')
                            ->label('Assigned To')
                            ->relationship('assignedUser', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'todo' => 'To Do',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                            ])
                            ->default('todo')
                            ->required(),

                        Forms\Components\DatePicker::make('start_date')
                            ->label('Start Date'),

                        Forms\Components\DatePicker::make('due_date')
                            ->label('Due Date'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('OKR Connection')
                    ->description('Kontribusi task terhadap objective')
                    ->schema([
                        Forms\Components\TextInput::make('contribution_percentage')
                            ->label('Kontribusi (%)')
                            ->numeric()
                            ->default(100)
                            ->minValue(1)
                            ->maxValue(100)
                            ->suffix('%')
                            ->helperText('Seberapa besar kontribusi task ini terhadap objective'),
                    ])
                    ->columns(1),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Task Name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('project.name')
                    ->label('Kegiatan')
                    ->badge()
                    ->color('info')
                    ->sortable(),

                Tables\Columns\TextColumn::make('assignedUser.name')
                    ->label('Assigned To')
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'todo' => 'secondary',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'todo' => 'To Do',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        default => ucfirst($state),
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('pivot.contribution_percentage')
                    ->label('Kontribusi')
                    ->suffix('%')
                    ->sortable(),

                Tables\Columns\TextColumn::make('start_date')
                    ->label('Start Date')
                    ->date()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('due_date')
                    ->label('Due Date')
                    ->date()
                    ->sortable()
                    ->color(fn($record) => $record->due_date && $record->due_date->isPast() && $record->status !== 'completed' ? 'danger' : null)
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'todo' => 'To Do',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                    ]),
                Tables\Filters\SelectFilter::make('assigned_to')
                    ->relationship('assignedUser', 'name'),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->label('Hubungkan Task Existing')
                    ->form(fn(Tables\Actions\AttachAction $action): array => [
                        $action->getRecordSelect()
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search) {
                                return Task::where('name', 'like', "%{$search}%")
                                    ->with(['project', 'assignedUser'])
                                    ->limit(50)
                                    ->get()
                                    ->mapWithKeys(function ($task) {
                                        return [$task->id => "{$task->name} ({$task->project->name})"];
                                    });
                            }),
                        Forms\Components\TextInput::make('contribution_percentage')
                            ->label('Kontribusi (%)')
                            ->numeric()
                            ->default(100)
                            ->minValue(1)
                            ->maxValue(100)
                            ->required(),
                    ])
                    ->preloadRecordSelect(),

                Tables\Actions\CreateAction::make()
                    ->label('Buat Task Baru')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['created_by'] = Auth::id();
                        return $data;
                    })
                    ->using(function (array $data): Task {
                        $contributionPercentage = $data['contribution_percentage'] ?? 100;
                        unset($data['contribution_percentage']);

                        $task = Task::create($data);

                        // Attach to objective with contribution percentage
                        $this->getOwnerRecord()->tasks()->attach($task->id, [
                            'contribution_percentage' => $contributionPercentage
                        ]);

                        return $task;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Edit')
                    ->form([
                        Forms\Components\TextInput::make('contribution_percentage')
                            ->label('Kontribusi (%)')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(100)
                            ->required(),
                    ])
                    ->using(function (array $data, $record): void {
                        // Update pivot data
                        $this->getOwnerRecord()->tasks()->updateExistingPivot($record->id, [
                            'contribution_percentage' => $data['contribution_percentage']
                        ]);
                    })
                    ->fillForm(function ($record): array {
                        return [
                            'contribution_percentage' => $record->pivot->contribution_percentage ?? 100,
                        ];
                    }),

                Tables\Actions\DetachAction::make()
                    ->label('Lepas Hubungan'),

                Tables\Actions\Action::make('view_task')
                    ->label('Lihat Detail')
                    ->icon('heroicon-o-eye')
                    ->url(fn($record): string => url("/admin/tasks/{$record->id}"))
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make()
                        ->label('Lepas Hubungan Terpilih'),
                ]),
            ])
            ->defaultSort('tasks.created_at', 'desc')
            ->striped();
    }
}
