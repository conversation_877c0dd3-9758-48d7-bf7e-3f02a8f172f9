<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Divisi extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'divisi';

    protected $fillable = [
        'nama_divisi',
        'deskripsi',
        'created_by',
        'departemen_id',
    ];

    protected $dates = ['deleted_at'];

    public function departemen()
    {
        return $this->belongsTo(Departemen::class);
    }

    public function karyawan()
    {
        return $this->hasMany(Karyawan::class, 'id_divisi');
    }

    public function objectives()
    {
        return $this->hasMany(Objective::class);
    }

    public function sopDokumens()
    {
        return $this->hasMany(SopDokumen::class);
    }
}
